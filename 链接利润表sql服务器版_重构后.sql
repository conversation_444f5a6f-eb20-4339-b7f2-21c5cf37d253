-- ****************************************************************************************** --
-- ** 注意：此脚本为重构优化版，逻辑与原版一致，但结构更清晰、性能更优 ** --
-- ** 核心思想：1. 基础表只读一次；2. 先聚合再关联(JOIN)；3. CTE模块化 ** --
-- ****************************************************************************************** --

-- 删除2025-07-03的数据，准备重新插入（针对性修复）
DELETE FROM 链接利润结果表
WHERE 付款日期 >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
  AND 付款日期 <= CURDATE();

-- 然后插入新计算的数据
INSERT INTO 链接利润结果表 (
    付款日期,
    平台货品ID,
    店铺,
    销售额,
    平台扣点,
    平台扣点费用,
    货品成本,
    售前退款金额,
    退款不退货金额,
    售前退款货品成本,
    退款金额平台扣点费用,
    退货退款金额,
    退货退款货品成本,
    邮费成本,
    售前退款邮资,
    推广费,
    商品毛利,
    商品毛利率,
    链接毛利,
    链接毛利率,
    运营发布人,
    发布日期,
    上架月数,
    上架时间权重,
    店铺权重,
    品类,
    品类提点,
    商品级别
)
WITH
-- =================================================================================
-- ** 步骤 1: 基础数据源准备 (对大表进行一次性过滤和预处理) **
-- =================================================================================

-- 1.1: 订单明细基础表：只读取一次订单明细，并应用所有通用过滤器
FilteredOrderDetails AS (
    SELECT
        DATE_FORMAT(付款时间, '%Y-%m-%d') as 付款日期,
        订单编号,
        原始子单号,
        店铺名称 as 店铺,
        平台货品ID,
        订单状态,
        已付,
        货品当前成本,
        预估邮资成本
    FROM 订单明细
    WHERE 标记名称 != '贴贴贴贴贴'
      AND 订单状态 != '线下退款'
      AND NOT (订单来源 = '手工创建' AND 订单状态 = '已取消')
      AND DATE(付款时间) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
      AND DATE(付款时间) <= CURDATE()
),

-- 1.2: 退款基础表：只读取一次原始退款单，聚合所有类型的退款
AggregatedRefunds_Base AS (
    SELECT
        原始子单,
        SUM(CASE WHEN 类型 = '售前退款' AND 平台状态 = '退款成功' THEN 实际退款金额 ELSE 0 END) as 售前退款金额,
        SUM(CASE WHEN 类型 = '退款不退货' AND 平台状态 = '退款成功' THEN 实际退款金额 ELSE 0 END) as 退款不退货金额,
        SUM(CASE WHEN 类型 = '退货退款' AND 平台状态 = '退款成功' THEN 实际退款金额 ELSE 0 END) as 退货退款金额
    FROM 原始退款单
    -- 优化：仅筛选包含相关退款类型的记录，减少处理数据量
    WHERE 类型 IN ('售前退款', '退款不退货', '退货退款') AND 平台状态 = '退款成功'
    GROUP BY 原始子单
),

-- =================================================================================
-- ** 步骤 2: 核心指标聚合 (各业务模块独立计算，得到小规模聚合结果) **
-- =================================================================================

-- 2.1: 销售数据聚合：计算每日、每个货品的销售额与成本
DailyProductSales AS (
    SELECT
        付款日期,
        平台货品ID,
        店铺,
        SUM(已付) as 销售额,
        SUM(货品当前成本) as 货品成本
    FROM FilteredOrderDetails
    GROUP BY 付款日期, 平台货品ID, 店铺
),

-- 2.2: 退款数据聚合：计算每日、每个货品的各类退款金额与退款成本
-- << 修复退款金额重复计算问题：按已付金额比例分摊退款金额到每个订单明细 >>
DailyProductRefunds AS (
    SELECT
        fod.付款日期,
        fod.平台货品ID,
        fod.店铺,
        -- << 修复：按已付金额比例分摊退款金额 >>
        SUM(CASE
            WHEN rf.原始子单 IS NOT NULL AND suborder_total.总已付 > 0 THEN
                rf.售前退款金额 * (fod.已付 / suborder_total.总已付)
            ELSE 0
        END) as 售前退款金额,
        SUM(CASE
            WHEN rf.原始子单 IS NOT NULL AND suborder_total.总已付 > 0 THEN
                rf.退款不退货金额 * (fod.已付 / suborder_total.总已付)
            ELSE 0
        END) as 退款不退货金额,
        SUM(CASE
            WHEN rf.原始子单 IS NOT NULL AND suborder_total.总已付 > 0 THEN
                rf.退货退款金额 * (fod.已付 / suborder_total.总已付)
            ELSE 0
        END) as 退货退款金额,
        -- 货品成本可以正常汇总（每个明细的成本是独立的）
        SUM(CASE
            WHEN fod.订单状态 = '已取消' THEN fod.货品当前成本 -- 条件1: 已取消订单
            WHEN rf.售前退款金额 > 0 THEN fod.货品当前成本    -- 条件2: 售前退款订单
            ELSE 0
        END) as 售前退款货品成本,
        SUM(CASE WHEN rf.退货退款金额 > 0 THEN fod.货品当前成本 ELSE 0 END) as 退货退款货品成本
    FROM FilteredOrderDetails fod -- << 以包含所有订单的FilteredOrderDetails为基础
    LEFT JOIN AggregatedRefunds_Base rf ON fod.原始子单号 = rf.原始子单 -- << LEFT JOIN 退款信息
    -- << 新增：计算每个原始子单的总已付金额，用于比例分摊 >>
    LEFT JOIN (
        SELECT
            原始子单号,
            SUM(已付) as 总已付
        FROM (
            SELECT
                原始子单号,
                已付
            FROM 订单明细
            WHERE 标记名称 != '贴贴贴贴贴'
              AND 订单状态 != '线下退款'
              AND NOT (订单来源 = '手工创建' AND 订单状态 = '已取消')
              AND DATE(付款时间) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
              AND DATE(付款时间) <= CURDATE()
        ) FilteredOrderDetails_ForTotal
        GROUP BY 原始子单号
    ) suborder_total ON fod.原始子单号 = suborder_total.原始子单号
    GROUP BY fod.付款日期, fod.平台货品ID, fod.店铺
),

-- 2.3: 邮费数据聚合：计算每日、每个货品的邮费成本和售前退款邮资 (这是最复杂的部分)
DailyProductPostage AS (
    WITH
    -- 2.3.1: 计算每个明细分摊到的邮费
    ApportionedPostageDetails AS (
        SELECT
            付款日期,
            平台货品ID,
            店铺,
            原始子单号,
            -- 按订单内已付金额比例分摊邮费
            CASE
                WHEN SUM(已付) OVER (PARTITION BY 订单编号) > 0 THEN
                    预估邮资成本 * (已付 / SUM(已付) OVER (PARTITION BY 订单编号))
                ELSE
                    -- 若订单总支付为0 (例如全部是已取消的0元单), 则平均分摊
                    预估邮资成本 / COUNT(*) OVER (PARTITION BY 订单编号)
            END as 分摊邮费
        FROM FilteredOrderDetails
    )
    -- 2.3.2: 按平台货品ID聚合邮费，并计算售前退款邮资
    SELECT
        apd.付款日期,
        apd.平台货品ID,
        apd.店铺,
        SUM(apd.分摊邮费) as 邮费成本,
        -- 售前退款邮资: 只计算那些真实发生了售前退款的子单所分摊的邮费
        SUM(CASE WHEN rf.原始子单 IS NOT NULL THEN apd.分摊邮费 ELSE 0 END) as 售前退款邮资
    FROM ApportionedPostageDetails apd
    -- 使用 LEFT JOIN，因为不是所有子单都有售前退款
    LEFT JOIN (
        SELECT DISTINCT 原始子单 FROM AggregatedRefunds_Base WHERE 售前退款金额 > 0
    ) rf ON apd.原始子单号 = rf.原始子单
    GROUP BY apd.付款日期, apd.平台货品ID, apd.店铺
),

-- 2.4: 推广费数据聚合
DailyProductPromotion AS (
    SELECT
        DATE_FORMAT(日期, '%Y-%m-%d') as 付款日期,
        主体ID as 平台货品ID,
        -- 注意：推广费没有店铺维度，关联时需要注意
        SUM(花费) as 推广费
    FROM 推广费
    WHERE DATE(日期) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
      AND DATE(日期) <= CURDATE()
    GROUP BY DATE_FORMAT(日期, '%Y-%m-%d'), 主体ID
),

-- =================================================================================
-- ** 步骤 3: 数据总装 (将所有聚合好的指标关联到统一的维度上) **
-- =================================================================================

-- 3.1: 创建一个包含所有“日-货品-店铺”组合的基础维度表
BaseDimensions AS (
    SELECT DISTINCT 付款日期, 平台货品ID, 店铺
    FROM FilteredOrderDetails
    -- 移除此处的过滤条件，以正确包含平台货品ID为空的记录
    -- WHERE 平台货品ID IS NOT NULL AND 平台货品ID != ''
),

-- 3.2: 将所有指标通过LEFT JOIN关联到基础维度上
CombinedData AS (
    SELECT
        -- 维度
        bd.付款日期,
        bd.平台货品ID,
        bd.店铺,
        -- 销售指标
        COALESCE(sales.销售额, 0) as 销售额,
        COALESCE(sales.货品成本, 0) as 货品成本,
        -- 退款指标
        COALESCE(refunds.售前退款金额, 0) as 售前退款金额,
        COALESCE(refunds.退款不退货金额, 0) as 退款不退货金额,
        COALESCE(refunds.退货退款金额, 0) as 退货退款金额,
        COALESCE(refunds.售前退款货品成本, 0) as 售前退款货品成本,
        COALESCE(refunds.退货退款货品成本, 0) as 退货退款货品成本,
        -- 邮费指标
        COALESCE(postage.邮费成本, 0) as 邮费成本,
        COALESCE(postage.售前退款邮资, 0) as 售前退款邮资,
        -- 推广费指标 (注意JOIN条件)
        COALESCE(promo.推广费, 0) as 推广费,
        -- 链接与平台信息
        link.运营发布人,
        link.发布日期,
        link.品类,
        link.商品级别,
        pf.平台扣点 as 平台扣点率
    FROM BaseDimensions bd
    LEFT JOIN DailyProductSales sales 
        ON bd.付款日期 = sales.付款日期 
        AND bd.店铺 = sales.店铺
        AND (bd.平台货品ID = sales.平台货品ID OR (bd.平台货品ID IS NULL AND sales.平台货品ID IS NULL))
    LEFT JOIN DailyProductRefunds refunds 
        ON bd.付款日期 = refunds.付款日期 
        AND bd.店铺 = refunds.店铺
        AND (bd.平台货品ID = refunds.平台货品ID OR (bd.平台货品ID IS NULL AND refunds.平台货品ID IS NULL))
    LEFT JOIN DailyProductPostage postage 
        ON bd.付款日期 = postage.付款日期 
        AND bd.店铺 = postage.店铺
        AND (bd.平台货品ID = postage.平台货品ID OR (bd.平台货品ID IS NULL AND postage.平台货品ID IS NULL))
    LEFT JOIN DailyProductPromotion promo 
        ON bd.付款日期 = promo.付款日期 
        AND (bd.平台货品ID = promo.平台货品ID OR (bd.平台货品ID IS NULL AND promo.平台货品ID IS NULL))
    LEFT JOIN 链接权重 link ON bd.平台货品ID = link.ID
    LEFT JOIN 平台扣点 pf ON bd.店铺 = pf.店铺名称
)

-- =================================================================================
-- ** 步骤 4: 最终计算与输出 (此部分逻辑与原版完全一致) **
-- =================================================================================

SELECT
    -- 维度列
    付款日期,
    平台货品ID,
    店铺,

    -- 基础指标
    销售额,
    CONCAT(ROUND(COALESCE(平台扣点率, 0) * 100, 2), '%') as 平台扣点,
    ROUND(销售额 * COALESCE(平台扣点率, 0), 4) as 平台扣点费用,
    货品成本,
    售前退款金额,
    退款不退货金额,
    售前退款货品成本,
    ROUND((售前退款金额 + 退款不退货金额) * COALESCE(平台扣点率, 0), 4) as 退款金额平台扣点费用,
    退货退款金额,
    退货退款货品成本,
    邮费成本,
    售前退款邮资,
    推广费,

    -- 商品毛利 (逻辑与原版完全一致)
    ROUND(
        (销售额 - 售前退款金额 - 退货退款金额 - 退款不退货金额)
        - (货品成本 - 售前退款货品成本 - 退货退款货品成本) -- 修改点：退货退款成本从“+”改为“-”，作为成本扣减
        - (邮费成本 - 售前退款邮资)
        - ((销售额 * COALESCE(平台扣点率, 0)) - ((售前退款金额 + 退款不退货金额) * COALESCE(平台扣点率, 0))),
        4
    ) as 商品毛利,

    -- 商品毛利率 (逻辑与原版完全一致)
    CONCAT(ROUND(
        LEAST(9999.99, GREATEST(-9999.99,
            (
                (
                    (销售额 - 售前退款金额 - 退货退款金额 - 退款不退货金额)
                    - (货品成本 - 售前退款货品成本 - 退货退款货品成本) -- 修改点：同步修改此处的成本计算
                    - (邮费成本 - 售前退款邮资)
                    - ((销售额 * COALESCE(平台扣点率, 0)) - ((售前退款金额 + 退款不退货金额) * COALESCE(平台扣点率, 0)))
                ) * 100.0
            ) / NULLIF( (销售额 - 售前退款金额 + 退款不退货金额), 0) -- 分母逻辑保持原样
        ))
    , 2), '%') as 商品毛利率,

    -- 链接毛利 (逻辑与原版完全一致)
    ROUND(
        (销售额 - 售前退款金额 - 退货退款金额 - 退款不退货金额)
        - (货品成本 - 售前退款货品成本 - 退货退款货品成本) -- 修改点：同步修改此处的成本计算
        - (邮费成本 - 售前退款邮资)
        - ((销售额 * COALESCE(平台扣点率, 0)) - ((售前退款金额 + 退款不退货金额) * COALESCE(平台扣点率, 0)))
        - 推广费,
        4
    ) as 链接毛利,

    -- 链接毛利率 (按照原版逻辑，即使存在计算不一致)
    CONCAT(ROUND(
        LEAST(9999.99, GREATEST(-9999.99,
            (
                -- 注意：此处的分子逻辑是为了精确匹配原版SQL，它与上面的“链接毛利”计算存在差异
                销售额
                - 货品成本
                - (售前退款金额 + 退款不退货金额)
                - 退货退款金额
                - 邮费成本 -- 差异1：此处未减去“售前退款邮资”
                - 推广费
                - (销售额 * COALESCE(平台扣点率, 0)) -- 直接使用平台扣点费用
                + 售前退款货品成本
                + ((售前退款金额 + 退款不退货金额) * COALESCE(平台扣点率, 0)) -- 直接使用退款金额平台扣点费用
                + 退货退款货品成本 -- 注意：此处的成本逻辑现已与“链接毛利”一致
            ) * 100.0
            / NULLIF(销售额 - 售前退款金额 - 退款不退货金额 - 退货退款金额, 0)
        ))
    , 2), '%') as 链接毛利率,

    -- 附加维度信息
    运营发布人,
    发布日期,
    CASE WHEN 发布日期 IS NOT NULL THEN TIMESTAMPDIFF(MONTH, 发布日期, CURDATE()) ELSE NULL END as 上架月数,
    CASE
        WHEN TIMESTAMPDIFF(MONTH, 发布日期, CURDATE()) > 24 THEN 0
        WHEN TIMESTAMPDIFF(MONTH, 发布日期, CURDATE()) > 12 THEN 0.5
        WHEN TIMESTAMPDIFF(MONTH, 发布日期, CURDATE()) >= 6 THEN 1
        ELSE 1.5 -- 恢复原始逻辑，当发布日期为NULL时，默认权重为1.5
    END as 上架时间权重,
    1 as 店铺权重, -- 保持原逻辑
    品类,
    CASE 品类
        WHEN '标品早期' THEN '1.80%'
        WHEN '标品中期' THEN '1.50%'
        WHEN '标品成熟期' THEN '1.32%'
        WHEN '半标品早期' THEN '1.30%'
        WHEN '半标品中期' THEN '1.10%'
        WHEN '半标品成熟期' THEN '0.90%'
        ELSE '0.00%'
    END as 品类提点,
    商品级别
FROM CombinedData
ORDER BY 付款日期 DESC, 平台货品ID;