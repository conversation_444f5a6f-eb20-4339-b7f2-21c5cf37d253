-- ******************************************************************* --
-- ** 注意：此脚本使用 先删除再插入 模式处理链接利润数据 (最近30天) ** --
-- ** 逻辑：先清空近30天数据，然后插入重新计算的近30天数据 ** --
-- ******************************************************************* --

-- 删除最近30天的数据，准备重新插入（保留30天之前的历史数据）
DELETE FROM 链接利润结果表
WHERE 付款日期 >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
  AND 付款日期 <= CURDATE();

-- 然后插入新计算的数据
INSERT INTO 链接利润结果表 (
    付款日期,
    平台货品ID,
    店铺,
    销售额,
    平台扣点,
    平台扣点费用,
    货品成本,
    售前退款金额,
    退款不退货金额,
    售前退款货品成本,
    退款金额平台扣点费用,
    退货退款金额,
    退货退款货品成本,
    邮费成本,
    售前退款邮资,
    推广费,
    商品毛利,
    商品毛利率,
    链接毛利,
    链接毛利率,
    运营发布人,
    发布日期,
    上架月数,
    上架时间权重,
    店铺权重,
    品类,
    品类提点,
    商品级别
)
WITH 订单商品销售额 AS (
    WITH 填充货品ID AS (
        SELECT 
            订单编号,
            COALESCE(
                平台货品ID,
                FIRST_VALUE(平台货品ID) OVER (
                    PARTITION BY 订单编号 
                    ORDER BY CASE WHEN 平台货品ID != '' AND 平台货品ID IS NOT NULL THEN 0 ELSE 1 END, 平台货品ID
                    ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
                )
            ) as 填充后平台货品ID,
            订单状态,
            应收金额,
            已付,
            订单明细退款状态,
            COUNT(*) OVER (PARTITION BY 订单编号) as 货品种类数,
            原始子单号 -- Assuming this column exists for joining later
        FROM 订单明细
        WHERE 标记名称 != '贴贴贴贴贴'
          AND 订单状态 != '线下退款'
          AND NOT (订单来源 = '手工创建' AND 订单状态 = '已取消')
        -- Removed退款成功 filter here, handle refund logic in dedicated CTEs
    )
    SELECT 
        订单编号,
        填充后平台货品ID as 平台货品ID,
        原始子单号,
        CASE 
            WHEN 订单状态 = '已取消' THEN 应收金额 / 货品种类数
            WHEN 已付 = 0 THEN 应收金额
            ELSE 已付 
        END as 实际金额,
        CASE 
            WHEN 订单状态 = '已取消' THEN 1 / 货品种类数
            ELSE CASE 
                WHEN 已付 = 0 THEN 应收金额
                ELSE 已付 
            END / NULLIF(SUM(CASE 
                WHEN 已付 = 0 THEN 应收金额
                ELSE 已付 
            END) OVER (PARTITION BY 订单编号), 0)
        END as 商品销售额占比
    FROM 填充货品ID
),

-- Start: New Refund Logic CTEs
原始退款单_聚合 AS (
    SELECT
        原始子单,
        SUM(实际退款金额) as 实际退款金额 -- Sum duplicates
    FROM 原始退款单 -- Replace with actual table name if different
    WHERE (类型 = '售前退款' OR 类型 = '退款不退货') -- Replace column names if different
    AND 平台状态 = '退款成功' -- Replace column name if different
    GROUP BY 原始子单
),

-- Start: New Return Logic CTE (similar to refund)
原始退货单_聚合 AS (
    SELECT
        原始子单,
        SUM(实际退款金额) as 实际退款金额 -- Sum duplicates
    FROM 原始退款单 -- Replace with actual table name if different
    WHERE 类型 = '退货退款' -- Filter for returns; Replace column name if different
    AND 平台状态 = '退款成功' -- Assuming this is also needed for returns; Replace column name if different
    GROUP BY 原始子单
),

-- Start: New CTE specifically for '退款不退货' type for cost calculation
售前退款子单_聚合 AS (
    SELECT
        原始子单,
        SUM(实际退款金额) as 实际退款金额 -- Still sum duplicates, though join only needs the key
    FROM 原始退款单 -- Replace with actual table name if different
    WHERE 类型 = '售前退款' -- << 修改为售前退款
    AND 平台状态 = '退款成功' -- Replace column name if different
    GROUP BY 原始子单
),
-- End: New CTE

-- << 提升 DedupOrderDetail 为顶级 CTE >>
DedupOrderDetail AS (
    SELECT DISTINCT
        平台货品ID,
        DATE_FORMAT(付款时间, '%Y-%m-%d') as 付款日期,
        原始子单号,
        店铺名称
    FROM 订单明细
    WHERE 标记名称 != '贴贴贴贴贴'
      AND 订单状态 != '线下退款'
      AND NOT (订单来源 = '手工创建' AND 订单状态 = '已取消')
      AND 平台货品ID IS NOT NULL
      AND 平台货品ID != ''
),

-- << 新增：仅聚合售前退款 >>
售前退款单_聚合 AS (
    SELECT
        原始子单,
        SUM(实际退款金额) as 实际退款金额
    FROM 原始退款单
    WHERE 类型 = '售前退款' -- << 只选售前退款
    AND 平台状态 = '退款成功'
    GROUP BY 原始子单
),

-- << 新增：仅聚合退款不退货 >>
退款不退货单_聚合 AS (
    SELECT
        原始子单,
        SUM(实际退款金额) as 实际退款金额
    FROM 原始退款单
    WHERE 类型 = '退款不退货' -- << 只选退款不退货
    AND 平台状态 = '退款成功'
    GROUP BY 原始子单
),

-- << 修改：移除内部 WITH，引用顶级 CTE >>
新退款金额_明细 AS (
    -- 移除了内部的 WITH DedupOrderDetail AS (...) 定义
    SELECT
        dod.平台货品ID,
        dod.付款日期,
        ROUND(SUM(COALESCE(rtk_sq.实际退款金额, 0)), 4) as 售前退款金额, -- << 计算售前退款
        ROUND(SUM(COALESCE(rtk_bth.实际退款金额, 0)), 4) as 退款不退货金额 -- << 计算退款不退货
    FROM DedupOrderDetail dod -- << 引用顶级的 DedupOrderDetail
    LEFT JOIN 售前退款单_聚合 rtk_sq ON dod.原始子单号 = rtk_sq.原始子单 -- << JOIN 售前
    LEFT JOIN 退款不退货单_聚合 rtk_bth ON dod.原始子单号 = rtk_bth.原始子单 -- << JOIN 不退货
    GROUP BY
        dod.平台货品ID,
        dod.付款日期
),

-- Start: New Refund Cost/Fee Logic CTE
新退款成本费用_明细 AS (
    -- This CTE now ONLY calculates 售前退款货品成本, without deduplication
    SELECT
        od.平台货品ID,
        DATE_FORMAT(od.付款时间, '%Y-%m-%d') as 付款日期,
        ROUND(SUM(od.货品当前成本), 4) as 售前退款货品成本 -- << 修改列名
    FROM 订单明细 od
    JOIN 售前退款子单_聚合 rtk_sq_cost ON od.原始子单号 = rtk_sq_cost.原始子单 -- << 修改 JOIN
    WHERE od.标记名称 != '贴贴贴贴贴'
      AND od.订单状态 != '线下退款'
      AND NOT (od.订单来源 = '手工创建' AND od.订单状态 = '已取消')
      AND od.平台货品ID IS NOT NULL -- Keep basic filters
      AND od.平台货品ID != ''
    GROUP BY
        od.平台货品ID,
        DATE_FORMAT(od.付款时间, '%Y-%m-%d')
),
-- End: New Refund Cost/Fee Logic CTE

退货数据 AS (
    WITH DedupOrderDetailForReturn AS (
        SELECT DISTINCT
            平台货品ID,
            DATE_FORMAT(付款时间, '%Y-%m-%d') as 付款日期,
            原始子单号
        FROM 订单明细
        WHERE 标记名称 != '贴贴贴贴贴'
          AND 订单状态 != '线下退款'
          AND NOT (订单来源 = '手工创建' AND 订单状态 = '已取消')
          AND 平台货品ID IS NOT NULL
          AND 平台货品ID != ''
    )
    SELECT
        dod.平台货品ID,
        dod.付款日期,
        ROUND(SUM(COALESCE(rth.实际退款金额, 0)), 4) as 退货退款金额
    FROM DedupOrderDetailForReturn dod
    JOIN 原始退货单_聚合 rth ON dod.原始子单号 = rth.原始子单 -- Join deduplicated details with aggregated returns
    GROUP BY
        dod.平台货品ID,
        dod.付款日期
),

-- << 修改 邮费数据 CTE，使其计算并输出 邮费成本 和 售前退款邮资 >>
邮费数据 AS (
    SELECT
        t.平台货品ID, -- 可能为 NULL 或 ''
        t.付款日期,
        ROUND(SUM(t.分摊邮费), 4) as 邮费成本,
        ROUND(SUM(CASE WHEN rtk_sq_flag.原始子单 IS NOT NULL THEN t.分摊邮费 ELSE 0 END), 4) as 售前退款邮资 -- << 在这里计算售前退款邮资
    FROM (
        -- 内部子查询计算单行分摊邮费
        SELECT
            原始子单号, -- << 确保选择了原始子单号
            平台货品ID,
            订单编号, 
            订单状态, 
            付款时间, 
            已付,      
            预估邮资成本, 
            标记名称, 
            DATE_FORMAT(付款时间, '%Y-%m-%d') as 付款日期,

            -- << 修改分摊邮费逻辑：包含已取消订单 >>
            COALESCE(
                CASE
                    -- 判断订单非取消部分的总已付是否大于0
                    WHEN SUM(CASE WHEN 订单状态 != '已取消' THEN 已付 ELSE 0 END) OVER (PARTITION BY 订单编号) > 0 THEN
                        -- 按非取消部分的总已付比例分摊当前行成本
                        预估邮资成本 * (已付 / NULLIF(SUM(CASE WHEN 订单状态 != '已取消' THEN 已付 ELSE 0 END) OVER (PARTITION BY 订单编号), 0))
                    -- 判断订单是否有非取消行
                    WHEN COUNT(CASE WHEN 订单状态 != '已取消' THEN 1 END) OVER (PARTITION BY 订单编号) > 0 THEN
                        -- 按非取消行数均摊当前行成本
                        预估邮资成本 / NULLIF(COUNT(CASE WHEN 订单状态 != '已取消' THEN 1 END) OVER (PARTITION BY 订单编号), 0)
                    -- 若订单只包含已取消行
                    WHEN COUNT(*) OVER (PARTITION BY 订单编号) > 0 THEN
                         -- 按总行数均摊当前行成本
                         预估邮资成本 / COUNT(*) OVER (PARTITION BY 订单编号)
                    ELSE 0 -- 理论上不应发生
                END,
            0) as 分摊邮费
        FROM 订单明细
        WHERE 标记名称 != '贴贴贴贴贴' -- 应用基础过滤
          AND 订单状态 != '线下退款'
          AND NOT (订单来源 = '手工创建' AND 订单状态 = '已取消')
    ) t
    -- << JOIN 用于判断售前退款 >>
    LEFT JOIN 售前退款子单_聚合 rtk_sq_flag ON t.原始子单号 = rtk_sq_flag.原始子单
    GROUP BY t.平台货品ID, t.付款日期 -- << 按平台货品ID和付款日期分组
),

-- << 新增 CTE：计算每日空白ID的总邮费和涉及店铺数 >>
每日空白ID信息 AS (
    SELECT
        付款日期,
        ROUND(SUM(分摊邮费), 4) as 总空白ID邮费成本,
        COUNT(DISTINCT 店铺名称) as 涉及店铺数
    FROM (
        SELECT
            店铺名称,
            订单编号, -- << 需要添加以便使用窗口函数
            订单状态, -- << 需要添加以便使用窗口函数
            已付,    -- << 需要添加以便使用窗口函数
            预估邮资成本, -- << 需要添加以便使用窗口函数
            DATE_FORMAT(付款时间, '%Y-%m-%d') as 付款日期,
            -- << 修改分摊邮费逻辑：包含已取消订单 (与上面邮费数据CTE中的逻辑相同) >>
            COALESCE(
                CASE
                    WHEN SUM(CASE WHEN 订单状态 != '已取消' THEN 已付 ELSE 0 END) OVER (PARTITION BY 订单编号) > 0 THEN
                        预估邮资成本 * (已付 / NULLIF(SUM(CASE WHEN 订单状态 != '已取消' THEN 已付 ELSE 0 END) OVER (PARTITION BY 订单编号), 0))
                    WHEN COUNT(CASE WHEN 订单状态 != '已取消' THEN 1 END) OVER (PARTITION BY 订单编号) > 0 THEN
                        预估邮资成本 / NULLIF(COUNT(CASE WHEN 订单状态 != '已取消' THEN 1 END) OVER (PARTITION BY 订单编号), 0)
                    WHEN COUNT(*) OVER (PARTITION BY 订单编号) > 0 THEN
                         预估邮资成本 / COUNT(*) OVER (PARTITION BY 订单编号)
                    ELSE 0
                END,
            0) as 分摊邮费
        FROM 订单明细
        WHERE 标记名称 != '贴贴贴贴贴'
          AND 订单状态 != '线下退款'
          AND NOT (订单来源 = '手工创建' AND 订单状态 = '已取消')
          AND (平台货品ID IS NULL OR 平台货品ID = '') -- 只选空白ID
    ) t
    GROUP BY 付款日期
),

推广费数据 AS (
    SELECT 
        主体ID as 平台货品ID,
        DATE_FORMAT(日期, '%Y-%m-%d') as 付款日期,
        ROUND(SUM(花费), 4) as 推广费
    FROM 推广费
    -- << 查询最近30天推广费 >>
    WHERE DATE(日期) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) -- << 最近30天开始日期 >>
      AND DATE(日期) <= CURDATE() -- << 当前日期 >>
    GROUP BY 主体ID, DATE_FORMAT(日期, '%Y-%m-%d')
),

基础数据 AS (
    SELECT 
        DATE_FORMAT(a.付款时间, '%Y-%m-%d') as 付款日期,
        a.平台货品ID,
        a.店铺名称 as 店铺,
        ROUND(SUM(CASE 
            WHEN a.标记名称 = '贴贴贴贴贴' THEN 0
            ELSE a.已付
        END), 4) as 销售额,
        ROUND(SUM(a.货品当前成本), 4) as 货品成本,
        ROUND(COALESCE(refund_details.售前退款金额, 0), 4) as 售前退款金额,
        ROUND(COALESCE(refund_details.退款不退货金额, 0), 4) as 退款不退货金额,
        ROUND(COALESCE(b2.退货退款金额, 0), 4) as 退货退款金额,
        ROUND(CASE
            WHEN a.平台货品ID IS NOT NULL AND a.平台货品ID != '' THEN COALESCE(c.邮费成本, 0)
            ELSE
                CASE
                    WHEN COALESCE(ANY_VALUE(d_blank.涉及店铺数), 0) > 0 THEN -- 使用 ANY_VALUE()
                        COALESCE(ANY_VALUE(d_blank.总空白ID邮费成本), 0) / ANY_VALUE(d_blank.涉及店铺数) -- 使用 ANY_VALUE()
                    ELSE 0
                END
        END, 4) as 邮费成本,
        ROUND(COALESCE(c.售前退款邮资, 0), 4) as 售前退款邮资,
        ROUND(COALESCE(g.推广费, 0), 4) as 推广费,
        CONCAT(ROUND(COALESCE(h.平台扣点 * 100, 0), 2), '%') as 平台扣点,
        ROUND(SUM(CASE WHEN a.标记名称 = '贴贴贴贴贴' THEN 0 ELSE a.已付 END) * COALESCE(h.平台扣点, 0), 4) as 平台扣点费用,
        ROUND((COALESCE(refund_details.售前退款金额, 0) + COALESCE(refund_details.退款不退货金额, 0)) * COALESCE(h.平台扣点, 0), 4) as 退款金额平台扣点费用,
        ROUND(COALESCE(refund_costs_new.售前退款货品成本, 0), 4) as 售前退款货品成本,
        ROUND(COALESCE(ANY_VALUE(return_costs_new.退货退款货品成本), 0), 4) as 退货退款货品成本,
        d.运营发布人,
        d.发布日期,
        TIMESTAMPDIFF(MONTH, d.发布日期, CURDATE()) as 上架月数,
        CASE 
            WHEN TIMESTAMPDIFF(MONTH, d.发布日期, CURDATE()) > 24 THEN 0
            WHEN TIMESTAMPDIFF(MONTH, d.发布日期, CURDATE()) > 12 THEN 0.5
            WHEN TIMESTAMPDIFF(MONTH, d.发布日期, CURDATE()) >= 6 THEN 1
            ELSE 1.5
        END as 上架时间权重,
        1 as 店铺权重,
        d.品类,
        CONCAT(ROUND(
            CASE d.品类
                WHEN '标品早期' THEN 1.8
                WHEN '标品中期' THEN 1.5
                WHEN '标品成熟期' THEN 1.32
                WHEN '半标品早期' THEN 1.3
                WHEN '半标品中期' THEN 1.1
                WHEN '半标品成熟期' THEN 0.9
                ELSE 0
            END
        , 2), '%') as 品类提点,
        d.商品级别,
        CASE d.品类
            WHEN '标品早期' THEN 0.018
            WHEN '标品中期' THEN 0.015
            WHEN '标品成熟期' THEN 0.0132
            WHEN '半标品早期' THEN 0.013
            WHEN '半标品中期' THEN 0.011
            WHEN '半标品成熟期' THEN 0.009
            ELSE 0
        END as 品类提点值
    FROM 订单明细 a
    LEFT JOIN 新退款金额_明细 refund_details ON a.平台货品ID = refund_details.平台货品ID AND DATE_FORMAT(a.付款时间, '%Y-%m-%d') = refund_details.付款日期
    LEFT JOIN 新退款成本费用_明细 refund_costs_new ON a.平台货品ID = refund_costs_new.平台货品ID AND DATE_FORMAT(a.付款时间, '%Y-%m-%d') = refund_costs_new.付款日期
    LEFT JOIN 退货数据 b2 ON a.平台货品ID = b2.平台货品ID 
        AND DATE_FORMAT(a.付款时间, '%Y-%m-%d') = b2.付款日期
    LEFT JOIN (
        SELECT
            od_sub.平台货品ID,
            DATE_FORMAT(od_sub.付款时间, '%Y-%m-%d') as 付款日期,
            ROUND(SUM(od_sub.货品当前成本), 4) as 退货退款货品成本
        FROM 订单明细 od_sub
        JOIN (
            SELECT 原始子单
            FROM 原始退款单
            WHERE 类型 = '退货退款'
            AND 平台状态 = '退款成功'
            GROUP BY 原始子单
        ) rth_cost_sub ON od_sub.原始子单号 = rth_cost_sub.原始子单
        WHERE od_sub.标记名称 != '贴贴贴贴贴'
          AND od_sub.订单状态 != '线下退款'
          AND NOT (od_sub.订单来源 = '手工创建' AND od_sub.订单状态 = '已取消')
          AND od_sub.平台货品ID IS NOT NULL
          AND od_sub.平台货品ID != ''
        GROUP BY
            od_sub.平台货品ID,
            DATE_FORMAT(od_sub.付款时间, '%Y-%m-%d')
    ) return_costs_new ON a.平台货品ID = return_costs_new.平台货品ID AND DATE_FORMAT(a.付款时间, '%Y-%m-%d') = return_costs_new.付款日期
    LEFT JOIN 邮费数据 c ON (a.平台货品ID = c.平台货品ID OR (a.平台货品ID IS NULL AND c.平台货品ID IS NULL)) 
        AND DATE_FORMAT(a.付款时间, '%Y-%m-%d') = c.付款日期
    LEFT JOIN 每日空白ID信息 d_blank ON DATE_FORMAT(a.付款时间, '%Y-%m-%d') = d_blank.付款日期
    LEFT JOIN 链接权重 d ON a.平台货品ID = d.ID
    LEFT JOIN (
        SELECT 
            主体ID as 平台货品ID,
            日期,
            花费 as 推广费
        FROM 推广费
    ) g ON a.平台货品ID = g.平台货品ID
        AND DATE_FORMAT(a.付款时间, '%Y-%m-%d') = DATE_FORMAT(g.日期, '%Y-%m-%d')
    LEFT JOIN 平台扣点 h ON a.店铺名称 = h.店铺名称
    WHERE a.标记名称 != '贴贴贴贴贴'
      AND a.订单状态 != '线下退款'
      AND NOT (a.订单来源 = '手工创建' AND a.订单状态 = '已取消')
    -- << 查询最近30天订单明细 >>
      AND DATE(a.付款时间) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) -- << 最近30天开始日期 >>
      AND DATE(a.付款时间) <= CURDATE() -- << 当前日期 >>
    GROUP BY 
        DATE_FORMAT(a.付款时间, '%Y-%m-%d'),
        a.平台货品ID,
        a.店铺名称,
        refund_details.售前退款金额,
        refund_details.退款不退货金额,
        refund_costs_new.售前退款货品成本,
        b2.退货退款金额,
        c.邮费成本,
        c.售前退款邮资,
        g.推广费,
        h.平台扣点,
        d.运营发布人,
        d.发布日期,
        d.品类,
        d.商品级别
)

SELECT 
    付款日期,
    平台货品ID,
    店铺,
    销售额,
    平台扣点,
    平台扣点费用,
    货品成本,
    售前退款金额,
    退款不退货金额,
    售前退款货品成本,
    退款金额平台扣点费用,
    退货退款金额,
    退货退款货品成本,
    邮费成本,
    售前退款邮资,
    推广费,
    -- 商品毛利 计算 (按照用户提供的最终公式)
    ROUND(
        -- 净销售额部分
        (COALESCE(销售额, 0) - COALESCE(售前退款金额, 0) - COALESCE(退货退款金额, 0) - COALESCE(退款不退货金额, 0))
        -- 减去 净货品成本部分 (修改退货退款货品成本符号)
        - (COALESCE(货品成本, 0) - COALESCE(售前退款货品成本, 0) + COALESCE(退货退款货品成本, 0))
        -- 减去 净邮费成本部分
        - (COALESCE(邮费成本, 0) - COALESCE(售前退款邮资, 0))
        -- 减去 净平台扣点费用部分
        - (COALESCE(平台扣点费用, 0) - COALESCE(退款金额平台扣点费用, 0)),
        4 -- 保留4位小数
    ) as 商品毛利,
    -- 商品毛利率 计算 (按照用户提供的新公式, 并内联商品毛利的计算)
    CONCAT(ROUND(
        LEAST(9999.99, GREATEST(-9999.99,
            (
                (
                    -- 内联商品毛利的计算表达式
                    (COALESCE(销售额, 0) - COALESCE(售前退款金额, 0) - COALESCE(退货退款金额, 0) - COALESCE(退款不退货金额, 0))
                    - (COALESCE(货品成本, 0) - COALESCE(售前退款货品成本, 0) + COALESCE(退货退款货品成本, 0))
                    - (COALESCE(邮费成本, 0) - COALESCE(售前退款邮资, 0))
                    - (COALESCE(平台扣点费用, 0) - COALESCE(退款金额平台扣点费用, 0))
                )
            ) * 100.0
            / NULLIF( (COALESCE(销售额, 0) - COALESCE(售前退款金额, 0) + COALESCE(退款不退货金额, 0)) , 0)
        ))
    , 2), '%') as 商品毛利率,
    -- 链接毛利 计算 (按照用户提供的最终公式)
    ROUND(
        -- 净销售额部分
        (COALESCE(销售额, 0) - COALESCE(售前退款金额, 0) - COALESCE(退货退款金额, 0) - COALESCE(退款不退货金额, 0))
        -- 减去 净货品成本部分
        - (COALESCE(货品成本, 0) - COALESCE(售前退款货品成本, 0) + COALESCE(退货退款货品成本, 0))
        -- 减去 净邮费成本部分
        - (COALESCE(邮费成本, 0) - COALESCE(售前退款邮资, 0))
        -- 减去 净平台扣点费用部分
        - (COALESCE(平台扣点费用, 0) - COALESCE(退款金额平台扣点费用, 0))
        -- 再减去 推广费
        - COALESCE(推广费, 0),
        4 -- 保留4位小数
    ) as 链接毛利,
    -- 链接毛利率 计算
    CONCAT(ROUND(
        LEAST(9999.99, GREATEST(-9999.99, 
            (
                销售额 - 
                货品成本 - 
                (COALESCE(售前退款金额, 0) + COALESCE(退款不退货金额, 0)) -
                COALESCE(退货退款金额, 0) - 
                COALESCE(邮费成本, 0) -
                COALESCE(推广费, 0) -
                平台扣点费用 +
                COALESCE(售前退款货品成本, 0) +
                COALESCE(退款金额平台扣点费用, 0)
                + COALESCE(退货退款货品成本, 0)
            ) * 100 / 
            NULLIF(销售额 - (COALESCE(售前退款金额, 0) + COALESCE(退款不退货金额, 0)) - COALESCE(退货退款金额, 0), 0) 
        ))
    , 2), '%') as 链接毛利率,
    -- 附加列
    运营发布人,
    发布日期,
    上架月数,
    上架时间权重,
    店铺权重,
    品类,
    品类提点,
    商品级别
FROM 基础数据
ORDER BY 付款日期 DESC, 平台货品ID;
