-- ******************************************************************* --
-- ** SKU利润结果表建表脚本 ** --
-- ******************************************************************* --

-- 创建SKU利润结果表，用于存储按商家编码(SKU)维度统计的利润数据
CREATE TABLE IF NOT EXISTS `sku利润结果表` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `发货日期` date NOT NULL COMMENT '发货日期',
    `SKU` varchar(100) NOT NULL COMMENT '商家编码(SKU)',
    `销售额` decimal(15,4) DEFAULT 0.0000 COMMENT '销售额',
    `货品成本` decimal(15,4) DEFAULT 0.0000 COMMENT '货品成本',
    `售前退款金额` decimal(15,4) DEFAULT 0.0000 COMMENT '售前退款金额',
    `退款不退货金额` decimal(15,4) DEFAULT 0.0000 COMMENT '退款不退货金额',
    `售前退款货品成本` decimal(15,4) DEFAULT 0.0000 COMMENT '售前退款货品成本',
    `退款金额平台扣点费用` decimal(15,4) DEFAULT 0.0000 COMMENT '退款金额平台扣点费用',
    `退货退款金额` decimal(15,4) DEFAULT 0.0000 COMMENT '退货退款金额',
    `退货退款货品成本` decimal(15,4) DEFAULT 0.0000 COMMENT '退货退款货品成本',
    `邮费成本` decimal(15,4) DEFAULT 0.0000 COMMENT '邮费成本',
    `售前退款邮资` decimal(15,4) DEFAULT 0.0000 COMMENT '售前退款邮资',
    `推广费` decimal(15,4) DEFAULT 0.0000 COMMENT '推广费',
    `平台扣点费用` decimal(15,4) DEFAULT 0.0000 COMMENT '平台扣点费用',
    `SKU毛利` decimal(15,4) DEFAULT 0.0000 COMMENT 'SKU毛利',
    `SKU毛利率` varchar(20) DEFAULT '0.00%' COMMENT 'SKU毛利率',
    `创建时间` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    `更新时间` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_unique_sku_profit` (`发货日期`, `SKU`) COMMENT '发货日期+SKU唯一索引，支持UPSERT操作',
    KEY `idx_发货日期` (`发货日期`) COMMENT '发货日期索引',
    KEY `idx_SKU` (`SKU`) COMMENT 'SKU索引',
    KEY `idx_创建时间` (`创建时间`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='SKU利润结果表-按商家编码维度统计的利润数据';

-- 创建表后的说明
-- 1. 主键：自增ID
-- 2. 唯一键：(发货日期, SKU) - 支持INSERT ... ON DUPLICATE KEY UPDATE操作
-- 3. 数据类型：金额字段使用decimal(15,4)保证精度
-- 4. 字符集：utf8mb4_general_ci，与其他表保持一致
-- 5. 时间戳：自动记录创建和更新时间 