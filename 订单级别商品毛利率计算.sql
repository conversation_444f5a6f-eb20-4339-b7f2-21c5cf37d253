-- 订单级别毛利率计算SQL（修复GROUP BY和字符集问题）
WITH 订单基础信息 AS (
    SELECT 
        订单编号,
        DATE(付款时间) as 付款日期,
        店铺名称,
        订单状态,
        订单来源,
        COUNT(*) as 明细行数,
        COUNT(DISTINCT 平台货品ID) as 不同商品数,
        ROUND(SUM(已付), 2) as 订单总销售额_明细表,
        ROUND(SUM(货品当前成本), 2) as 订单总货品成本_明细表,
        ROUND(AVG(预估邮资成本), 2) as 订单预估邮资成本,
        GROUP_CONCAT(DISTINCT 平台货品ID) as 包含商品ID列表
    FROM 订单明细 
    WHERE 付款时间 >= '2025-06-01 00:00:00' 
    AND 付款时间 < '2025-06-29 00:00:00'
    AND 标记名称 != '贴贴贴贴贴'
    AND 订单状态 != '线下退款'
    AND NOT (订单来源 = '手工创建' AND 订单状态 = '已取消')
    AND 平台货品ID IS NOT NULL 
    AND 平台货品ID != ''
    GROUP BY 订单编号, DATE(付款时间), 店铺名称, 订单状态, 订单来源
),
订单利润数据 AS (
    SELECT 
        obi.订单编号,
        obi.付款日期,
        obi.店铺名称,
        obi.订单状态,
        obi.订单来源,
        obi.明细行数,
        obi.不同商品数,
        obi.订单总销售额_明细表,
        obi.订单总货品成本_明细表,
        obi.订单预估邮资成本,
        obi.包含商品ID列表,
        ROUND(SUM(lr.销售额), 2) as 订单总销售额_利润表,
        ROUND(SUM(lr.商品毛利), 2) as 订单总商品毛利,
        ROUND(SUM(IFNULL(lr.售前退款金额, 0)), 2) as 订单总售前退款,
        ROUND(SUM(IFNULL(lr.退款不退货金额, 0)), 2) as 订单总退款不退货,
        ROUND(SUM(IFNULL(lr.退货退款金额, 0)), 2) as 订单总退货退款,
        ROUND(SUM(lr.邮费成本), 2) as 订单总邮费成本,
        ROUND(SUM(lr.平台扣点费用), 2) as 订单总平台扣点费用,
        ROUND(SUM(lr.推广费), 2) as 订单总推广费
    FROM 订单基础信息 obi
    INNER JOIN 订单明细 od ON obi.订单编号 COLLATE utf8mb4_general_ci = od.订单编号 COLLATE utf8mb4_general_ci
        AND obi.付款日期 = DATE(od.付款时间)
        AND od.标记名称 != '贴贴贴贴贴'
    INNER JOIN 链接利润结果表 lr ON TRIM(od.平台货品ID) COLLATE utf8mb4_general_ci = TRIM(lr.平台货品ID) COLLATE utf8mb4_general_ci
        AND obi.付款日期 = lr.付款日期
    GROUP BY obi.订单编号, obi.付款日期, obi.店铺名称, obi.订单状态, obi.订单来源, 
             obi.明细行数, obi.不同商品数, obi.订单总销售额_明细表, obi.订单总货品成本_明细表, 
             obi.订单预估邮资成本, obi.包含商品ID列表
)
SELECT 
    *,
    -- 计算订单级别毛利率分母
    ROUND((订单总销售额_利润表 - 订单总售前退款 + 订单总退款不退货), 2) as 订单毛利率分母,
    -- 计算订单级别毛利率
    ROUND(
        CASE 
            WHEN (订单总销售额_利润表 - 订单总售前退款 + 订单总退款不退货) != 0 
            THEN (订单总商品毛利 * 100.0) / (订单总销售额_利润表 - 订单总售前退款 + 订单总退款不退货)
            ELSE 0 
        END, 2
    ) as 订单毛利率百分比
FROM 订单利润数据
ORDER BY 订单总销售额_利润表 DESC
LIMIT 20;