-- ******************************************************************* --
-- ** 注意：此脚本现在处理从2025-03-01到今天的数据，采用先删除再插入模式 ** --
-- ******************************************************************* --

-- 首先删除目标表中符合日期范围的数据
DELETE FROM 店铺利润结果表
WHERE 付款日期 >= '2025-03-01'
  AND 付款日期 <= CURDATE();

-- 然后插入新计算的数据
INSERT INTO 店铺利润结果表 (
    付款日期,
    部门名称,
    店铺名称,
    销售额,
    辅助销售额,
    退款金额,
    货品成本,
    预估邮资,
    平台扣点,
    商品毛利,
    商品毛利率,
    `营销返款（总）`,
    `营销返款（补）`,
    `S单成本`,
    推广费,
    拍摄成本,
    美工成本,
    链接固定成本,
    运营毛利,
    运营毛利率,
    场地租金公摊,
    固定公用摊销,
    人工成本,
    其他费用,
    净利润,
    净利润率
)
-- CTE 1: 计算每个店铺每月的总退款补偿金额 和 非公司原因的退款补偿金额
WITH MonthlyRefunds AS (
    SELECT
        店铺名称,
        月份,
        SUM(COALESCE(退款补偿金额, 0)) AS TotalMonthlyRefund,
        SUM(
            CASE
                WHEN `部门原因` NOT LIKE '%公司原因%' THEN COALESCE(退款补偿金额, 0)
                ELSE 0
            END
        ) AS NonCompanyReasonRefund
    FROM
        售后财务明细
    GROUP BY
        店铺名称,
        月份
),
-- CTE 2: 计算每个店铺每天的刷单成本总额
DailyBrushingCosts AS (
    SELECT
        日期,
        店铺名称,
        SUM(COALESCE(总刷单成本, 0)) AS TotalDailyBrushingCost
    FROM
        刷单成本明细
    GROUP BY
        日期,
        店铺名称
),
-- CTE 3: 计算每个店铺每月去重后的美工、拍摄和链接固定成本总额
MonthlyCreativeCosts AS (
    SELECT
        -- Convert only valid months
        MONTH(STR_TO_DATE(CONCAT(DistinctCosts.付款月份, '-01'), '%Y-%m-%d')) AS 月份数字,
        DistinctCosts.店铺,
        SUM(DistinctCosts.美工成本) AS TotalMonthlyArtCost,
        SUM(DistinctCosts.拍摄成本) AS TotalMonthlyPhotoCost,
        SUM(DistinctCosts.链接固定成本) AS TotalMonthlyLinkFixedCost
    FROM (
        -- Get distinct costs first
        SELECT DISTINCT
            付款月份,
            店铺,
            平台货品ID,
            COALESCE(美工成本, 0) AS 美工成本,
            COALESCE(拍摄成本, 0) AS 拍摄成本,
            COALESCE(链接固定成本, 0) AS 链接固定成本
        FROM
            员工提成结果表
        WHERE 平台货品ID IS NOT NULL AND (美工成本 > 0 OR 拍摄成本 > 0 OR 链接固定成本 > 0)
    ) AS DistinctCosts
    -- Filter for valid '付款月份' format AFTER getting distinct costs but BEFORE grouping
    WHERE
        DistinctCosts.付款月份 IS NOT NULL
        AND DistinctCosts.付款月份 REGEXP '^[0-9]{4}-[0-9]{2}$' -- Ensures 'YYYY-MM' format
    GROUP BY
        月份数字,
        店铺
),
-- 新增 CTE 4: 获取相关月份及天数
RelevantMonths AS (
    SELECT DISTINCT
        YEAR(付款日期) AS RYear,
        MONTH(付款日期) AS RMonth,
        DAY(LAST_DAY(付款日期)) AS DaysInMonth,
        LAST_DAY(付款日期) AS MonthEndDate,
        DATE_SUB(LAST_DAY(付款日期), INTERVAL DAY(LAST_DAY(付款日期)) - 1 DAY) AS MonthStartDate -- 月初日期
    FROM 链接利润结果表
),
-- 新增 CTE 5: 计算每个月有效的房租合同的 '金额-月' 总和
MonthlyRentSum AS (
    SELECT
        rm.RYear,
        rm.RMonth,
        SUM(COALESCE(fe.`金额-月`, 0)) AS TotalMonthlyRentValue
    FROM
        RelevantMonths rm
    JOIN
        固定开支 fe ON fe.项目类型 = '房租'
                      AND fe.`金额-月` IS NOT NULL -- 仅统计有月金额的记录
                      -- 合同在此月内有效（开始日期 <= 月底 且 结束日期 >= 月初）
                      AND fe.开始日期 <= rm.MonthEndDate
                      AND IFNULL(fe.结束日期, '9999-12-31') >= rm.MonthStartDate
    GROUP BY
        rm.RYear, rm.RMonth
),
-- 新增 CTE 6: 计算每个月的日平均租金
TotalMonthlyCompanyRent_Avg AS (
    SELECT
        mrs.RYear,
        mrs.RMonth,
        -- 计算该月日平均租金
        COALESCE(mrs.TotalMonthlyRentValue / rm.DaysInMonth, 0) AS DailyAvgRent
    FROM
        MonthlyRentSum mrs
    JOIN
        RelevantMonths rm ON mrs.RYear = rm.RYear AND mrs.RMonth = rm.RMonth
),
-- 新增 CTE 7: 计算每个月有效的 *非房租* 固定开支 '金额-月' 总和
MonthlyOtherFixedExpenseSum AS (
    SELECT
        rm.RYear,
        rm.RMonth,
        SUM(COALESCE(fe.`金额-月`, 0)) AS TotalMonthlyOtherValue
    FROM
        RelevantMonths rm
    JOIN
        固定开支 fe ON fe.项目类型 != '房租' -- 排除房租
                      AND fe.`金额-月` IS NOT NULL
                      -- 合同在此月内有效
                      AND fe.开始日期 <= rm.MonthEndDate
                      AND IFNULL(fe.结束日期, '9999-12-31') >= rm.MonthStartDate
    GROUP BY
        rm.RYear, rm.RMonth
),
-- 新增 CTE 8: 计算每个月的日平均 *非房租* 固定开支
TotalMonthlyCompanyOther_Avg AS (
    SELECT
        mofes.RYear,
        mofes.RMonth,
        COALESCE(mofes.TotalMonthlyOtherValue / rm.DaysInMonth, 0) AS DailyAvgOther
    FROM
        MonthlyOtherFixedExpenseSum mofes
    JOIN
        RelevantMonths rm ON mofes.RYear = rm.RYear AND mofes.RMonth = rm.RMonth
),
-- 新增 CTE 9: 计算每个员工在每个相关月份的适用月薪 (修改为包含员工标识和日期信息)
EmployeeApplicableMonthlySalary AS (
    SELECT
        s.姓名, -- Or another unique employee ID if available
        rm.RYear,
        rm.RMonth,
        rm.DaysInMonth,
        rm.MonthStartDate,
        rm.MonthEndDate,
        s.入职时间,
        -- Safely convert leave date string to date
        CASE
            WHEN s.离职时间 IS NULL OR s.离职时间 = '' THEN NULL
            ELSE STR_TO_DATE(s.离职时间, '%Y-%m-%d')
        END AS 离职日期_date,
        s.转正时间,
        -- Determine applicable MONTHLY salary based on end-of-month status
        CASE
            WHEN s.转正时间 IS NOT NULL AND rm.MonthEndDate >= s.转正时间 AND s.转正薪资 IS NOT NULL AND s.转正薪资 > 0 THEN s.转正薪资
            ELSE COALESCE(s.预估月薪, 0)
        END AS ApplicableMonthlySalary
    FROM
        RelevantMonths rm
    CROSS JOIN
        薪资表 s
    WHERE
        -- Filter for employees potentially active in the month to reduce intermediate data size
        s.入职时间 <= rm.MonthEndDate -- Started before or during the month end
        AND (
               s.离职时间 IS NULL
            OR s.离职时间 = ''
            OR STR_TO_DATE(s.离职时间, '%Y-%m-%d') >= rm.MonthStartDate -- Left during or after the month start
           )
        AND (s.离职时间 IS NULL OR s.离职时间 = '' OR STR_TO_DATE(s.离职时间, '%Y-%m-%d') IS NOT NULL) -- Ensure date conversion is valid if not NULL/empty
),
-- 新增 CTE 10: 计算公司 *每日* 的总人工成本 (基于日薪率)
TotalDailyCompanySalary_Exact AS (
    SELECT
        rd.Date AS 付款日期,
        -- Sum the daily rate for employees active on this specific day
        SUM(COALESCE(eams.ApplicableMonthlySalary / eams.DaysInMonth, 0)) AS TotalDailySalary
    FROM
        -- Use distinct dates from the source table
        (SELECT DISTINCT 付款日期 AS Date, YEAR(付款日期) as RYear, MONTH(付款日期) as RMonth FROM 链接利润结果表) rd
    JOIN
        EmployeeApplicableMonthlySalary eams ON rd.RYear = eams.RYear AND rd.RMonth = eams.RMonth
    WHERE
        -- Employee is active on this specific date (rd.Date)
        eams.入职时间 <= rd.Date
        AND (
               eams.离职日期_date IS NULL -- Still employed if leave date is NULL
            OR eams.离职日期_date > rd.Date  -- Still employed if leave date is strictly AFTER the current date
            )
    GROUP BY rd.Date
),
-- 重命名为 CTE 11: 计算每天的活跃店铺数量 (保持不变)
DailyActiveStores AS (
    SELECT
        付款日期,
        COUNT(DISTINCT 店铺) AS ActiveStoreCount
    FROM
        链接利润结果表
    GROUP BY
        付款日期
),
-- 新增 CTE 12: 计算每日总其他费用
DailyOtherExpenses AS (
    SELECT
        付款时间 AS 付款日期,
        SUM(COALESCE(报销金额, 0)) AS TotalDailyOtherExpense
    FROM
        其他费用 -- 从新创建的表获取数据
    GROUP BY
        付款时间
),
-- 新增：基于链接权重日期和订单明细店铺的每日店铺拍摄成本
DailyStorePhotographyCost AS (
    SELECT
        lw.店铺 AS 店铺名称,
        DATE(lw.拍摄日期) AS 成本发生日期,
        SUM(COALESCE(lw.拍照成本, 0)) AS 每日店铺拍摄成本
    FROM
        链接权重 lw
    WHERE lw.拍照成本 IS NOT NULL AND lw.拍照成本 > 0
      AND lw.拍摄日期 IS NOT NULL
      AND lw.店铺 IS NOT NULL AND lw.店铺 != '' -- 确保店铺字段有效
      AND DATE(lw.拍摄日期) >= '2025-03-01' -- 修改为指定开始日期
      AND DATE(lw.拍摄日期) <= CURDATE() -- 结束日期为当天
    GROUP BY
        lw.店铺,
        DATE(lw.拍摄日期)
),
-- 新增：基于链接权重日期和订单明细店铺的每日店铺美工成本
DailyStoreArtCost AS (
    SELECT
        lw.店铺 AS 店铺名称,
        DATE(lw.美工日期) AS 成本发生日期,
        SUM(COALESCE(lw.美工成本, 0)) AS 每日店铺美工成本
    FROM
        链接权重 lw
    WHERE lw.美工成本 IS NOT NULL AND lw.美工成本 > 0
      AND lw.美工日期 IS NOT NULL
      AND lw.店铺 IS NOT NULL AND lw.店铺 != '' -- 确保店铺字段有效
      AND DATE(lw.美工日期) >= '2025-03-01' -- 修改为指定开始日期
      AND DATE(lw.美工日期) <= CURDATE() -- 结束日期为当天
    GROUP BY
        lw.店铺,
        DATE(lw.美工日期)
),
-- 新增：基于推广费表日期和店铺的每日店铺推广费
DailyStorePromotionCost AS (
    SELECT
        fp.`date` AS 日期,      -- 修改来源表和字段名
        fp.`shop` AS 店铺,      -- 修改来源表和字段名
        ROUND(SUM(fp.`amount`), 2) AS 总花费  -- 修改来源表和字段名
    FROM
        fee_prom fp             -- 修改来源表
    WHERE fp.`date` >= '2025-03-01' -- 与主查询日期范围保持一致
      AND fp.`date` <= CURDATE()
      AND fp.`shop` IS NOT NULL AND fp.`shop` != '' -- 确保店铺字段有效
    GROUP BY
        fp.`date`, fp.`shop`      -- 修改来源表和字段名
)
-- 主查询 (处理最近30天)
SELECT
    lr.付款日期,
    dept.部门名称,
    lr.店铺 AS 店铺名称,
    -- 修改：销售额 = 销售额 - 售前退款金额 - 退货退款金额 - 退款不退货金额
    ROUND(SUM(COALESCE(lr.销售额, 0) - COALESCE(lr.售前退款金额, 0) - COALESCE(lr.退货退款金额, 0) - COALESCE(lr.退款不退货金额, 0)), 4) AS 销售额,
    -- 新增：辅助销售额 = 链接利润结果表中的原始销售额汇总
    ROUND(SUM(COALESCE(lr.销售额, 0)), 4) AS 辅助销售额,
    ROUND(SUM(COALESCE(lr.售前退款金额, 0) + COALESCE(lr.退款不退货金额, 0)), 4) AS 退款金额, -- 这个计算方式不变 (根据之前确认)
    -- 修改：货品成本 = 货品成本 - 售前退款货品成本 - 退货退款货品成本 (修正逻辑)
    ROUND(SUM(COALESCE(lr.货品成本, 0) - COALESCE(lr.售前退款货品成本, 0) - COALESCE(lr.退货退款货品成本, 0)), 4) AS 货品成本,
    -- 修改：预估邮资 = 邮费成本 - 售前退款邮资
    ROUND(SUM(COALESCE(lr.邮费成本, 0) - COALESCE(lr.售前退款邮资, 0)), 4) AS 预估邮资,
    -- 修改：平台扣点 = 平台扣点费用 - 退款金额平台扣点费用
    ROUND(SUM(COALESCE(lr.平台扣点费用, 0) - COALESCE(lr.退款金额平台扣点费用, 0)), 4) AS 平台扣点,
    -- 计算商品毛利 (修改为直接汇总)
    ROUND(SUM(COALESCE(lr.商品毛利, 0)), 4) AS 商品毛利,
    -- 计算商品毛利率 (修改分子)
    CONCAT(FORMAT(
        (
            SUM(COALESCE(lr.商品毛利, 0)) /
            NULLIF(SUM(lr.销售额) - SUM(lr.退款金额), 0)
        ) * 100,
    2), '%') AS 商品毛利率,
    -- 每日分摊总返款额
    ROUND(
        MAX(COALESCE(mr.TotalMonthlyRefund / DAY(LAST_DAY(lr.付款日期)), 0)),
        4
    ) AS `营销返款（总）`,
    -- 每日分摊*非*公司原因返款额
    ROUND(
        MAX(COALESCE(mr.NonCompanyReasonRefund / DAY(LAST_DAY(lr.付款日期)), 0)),
        4
    ) AS `营销返款（补）`,
    -- 每日刷单成本
    ROUND(COALESCE(MAX(dbc.TotalDailyBrushingCost), 0), 4) AS `S单成本`,
    -- 推广费
    COALESCE(MAX(dspc2.总花费), 0) AS 推广费,
    -- 更新: 拍摄成本从 DailyStorePhotographyCost 获取
    COALESCE(MAX(dspc.每日店铺拍摄成本), 0) AS `拍摄成本`,
    -- 更新: 美工成本从 DailyStoreArtCost 获取
    COALESCE(MAX(dsac.每日店铺美工成本), 0) AS `美工成本`,
    -- 新增: 每日分摊链接固定成本
    ROUND(
        MAX(COALESCE(mcc.TotalMonthlyLinkFixedCost / DAY(LAST_DAY(lr.付款日期)), 0)),
        4
    ) AS `链接固定成本`,
    -- 新增: 运营毛利 (修改商品毛利部分)
    ROUND(
        -- 商品毛利 (修改为直接汇总)
        SUM(COALESCE(lr.商品毛利, 0))
        -- - 营销返款（总）
        - MAX(COALESCE(mr.TotalMonthlyRefund / DAY(LAST_DAY(lr.付款日期)), 0))
        -- + 营销返款（补）
        + MAX(COALESCE(mr.NonCompanyReasonRefund / DAY(LAST_DAY(lr.付款日期)), 0))
        -- - S单成本
        - COALESCE(MAX(dbc.TotalDailyBrushingCost), 0)
        -- - 推广费
        - COALESCE(MAX(dspc2.总花费), 0)
        -- - 拍摄成本 (更新)
        - COALESCE(MAX(dspc.每日店铺拍摄成本), 0)
        -- - 美工成本 (更新)
        - COALESCE(MAX(dsac.每日店铺美工成本), 0)
        -- - 链接固定成本
        - MAX(COALESCE(mcc.TotalMonthlyLinkFixedCost / DAY(LAST_DAY(lr.付款日期)), 0)),
    4) AS `运营毛利`,
    -- 新增: 运营毛利率 (修改分子部分)
    CONCAT(FORMAT(
        (
            -- 运营毛利 (分子部分修改)
            SUM(COALESCE(lr.商品毛利, 0))
            - MAX(COALESCE(mr.TotalMonthlyRefund / DAY(LAST_DAY(lr.付款日期)), 0))
            + MAX(COALESCE(mr.NonCompanyReasonRefund / DAY(LAST_DAY(lr.付款日期)), 0))
            - COALESCE(MAX(dbc.TotalDailyBrushingCost), 0)
            - COALESCE(MAX(dspc2.总花费), 0)
            - COALESCE(MAX(dspc.每日店铺拍摄成本), 0) -- 拍摄成本 (更新)
            - COALESCE(MAX(dsac.每日店铺美工成本), 0) -- 美工成本 (更新)
            - MAX(COALESCE(mcc.TotalMonthlyLinkFixedCost / DAY(LAST_DAY(lr.付款日期)), 0))
        ) / NULLIF(SUM(lr.销售额) - SUM(lr.退款金额), 0) -- 除以 (销售额 - 退款金额)
    * 100, 2), '%') AS `运营毛利率`,
    -- 修改: 场地租金公摊 (使用月平均方法)
    ROUND(
        COALESCE(MAX(tmcra.DailyAvgRent) / MAX(das.ActiveStoreCount), 0),
        4
    ) AS `场地租金公摊`,
    -- 新增: 固定公用摊销 (使用月平均方法，排除房租)
    ROUND(
        COALESCE(MAX(tmcoa.DailyAvgOther) / MAX(das.ActiveStoreCount), 0),
        4
    ) AS `固定公用摊销`,
    -- 新增: 人工成本 (使用每日精确方法)
    ROUND(
        COALESCE(MAX(tdcs.TotalDailySalary) / MAX(das.ActiveStoreCount), 0),
        4
    ) AS `人工成本`,
    -- 新增: 其他费用摊销 (来自 其他费用 表)
    ROUND(
        COALESCE(MAX(doe.TotalDailyOtherExpense) / MAX(das.ActiveStoreCount), 0),
        4
    ) AS `其他费用`,
    -- 新增: 净利润 (修改商品毛利部分, 减去其他费用)
    ROUND(
        -- 商品毛利 (修改为直接汇总)
        SUM(COALESCE(lr.商品毛利, 0))
        -- - 场地租金公摊
        - COALESCE(MAX(tmcra.DailyAvgRent) / MAX(das.ActiveStoreCount), 0)
        -- - 人工成本
        - COALESCE(MAX(tdcs.TotalDailySalary) / MAX(das.ActiveStoreCount), 0)
        -- - 固定公用摊销
        - COALESCE(MAX(tmcoa.DailyAvgOther) / MAX(das.ActiveStoreCount), 0)
        -- - 营销返款（总）
        - MAX(COALESCE(mr.TotalMonthlyRefund / DAY(LAST_DAY(lr.付款日期)), 0))
        -- - S单成本
        - COALESCE(MAX(dbc.TotalDailyBrushingCost), 0)
        -- - 推广费
        - COALESCE(MAX(dspc2.总花费), 0)
        -- - 其他费用摊销 (新增)
        - COALESCE(MAX(doe.TotalDailyOtherExpense) / MAX(das.ActiveStoreCount), 0),
    4) AS `净利润`,
    -- 新增: 净利润率 (修改分子中的商品毛利部分, 减去其他费用)
    CONCAT(FORMAT(
        (
            -- 净利润 (分子部分修改)
            SUM(COALESCE(lr.商品毛利, 0))
            - COALESCE(MAX(tmcra.DailyAvgRent) / MAX(das.ActiveStoreCount), 0)
            - COALESCE(MAX(tdcs.TotalDailySalary) / MAX(das.ActiveStoreCount), 0)
            - COALESCE(MAX(tmcoa.DailyAvgOther) / MAX(das.ActiveStoreCount), 0)
            - MAX(COALESCE(mr.TotalMonthlyRefund / DAY(LAST_DAY(lr.付款日期)), 0))
            - COALESCE(MAX(dbc.TotalDailyBrushingCost), 0)
            -- - 其他费用摊销 (新增)
            - COALESCE(MAX(doe.TotalDailyOtherExpense) / MAX(das.ActiveStoreCount), 0)
        ) / NULLIF(SUM(lr.销售额) - SUM(lr.退款金额), 0) -- 除以 (销售额 - 退款金额)
    * 100, 2), '%') AS `净利润率`
FROM
    链接利润结果表 lr
LEFT JOIN -- 连接月度返款数据
    MonthlyRefunds mr ON lr.店铺 = mr.店铺名称 COLLATE utf8mb4_general_ci
                      AND MONTH(lr.付款日期) = mr.月份
LEFT JOIN -- 连接每日刷单成本数据
    DailyBrushingCosts dbc ON lr.付款日期 = dbc.日期
                          AND lr.店铺 = dbc.店铺名称 COLLATE utf8mb4_general_ci
LEFT JOIN -- 连接月度创意成本数据
    MonthlyCreativeCosts mcc ON lr.店铺 = mcc.店铺 COLLATE utf8mb4_general_ci
                           AND MONTH(lr.付款日期) = mcc.月份数字
LEFT JOIN -- 连接部门数据
    店铺部门对应表 dept ON lr.店铺 = dept.店铺名称 COLLATE utf8mb4_general_ci
LEFT JOIN -- 修改：连接月度日平均租金
    TotalMonthlyCompanyRent_Avg tmcra ON YEAR(lr.付款日期) = tmcra.RYear
                                     AND MONTH(lr.付款日期) = tmcra.RMonth
LEFT JOIN -- 连接每日活跃店铺数
    DailyActiveStores das ON lr.付款日期 = das.付款日期
LEFT JOIN -- 新增：连接月度日平均非房租固定开支
    TotalMonthlyCompanyOther_Avg tmcoa ON YEAR(lr.付款日期) = tmcoa.RYear
                                       AND MONTH(lr.付款日期) = tmcoa.RMonth
LEFT JOIN -- Correctly join the daily exact salary CTE
    TotalDailyCompanySalary_Exact tdcs ON lr.付款日期 = tdcs.付款日期
LEFT JOIN -- 新增: 连接每日总其他费用
    DailyOtherExpenses doe ON lr.付款日期 = doe.付款日期
LEFT JOIN DailyStorePhotographyCost dspc
    ON lr.店铺 COLLATE utf8mb4_general_ci = dspc.店铺名称 COLLATE utf8mb4_general_ci
    AND lr.付款日期 = dspc.成本发生日期
LEFT JOIN DailyStoreArtCost dsac
    ON lr.店铺 COLLATE utf8mb4_general_ci = dsac.店铺名称 COLLATE utf8mb4_general_ci
    AND lr.付款日期 = dsac.成本发生日期
LEFT JOIN DailyStorePromotionCost dspc2 -- dspc2 是 DailyStorePromotionCost 的别名
    ON lr.付款日期 = dspc2.日期  -- 确保连接条件正确
    AND lr.店铺 COLLATE utf8mb4_general_ci = dspc2.店铺 -- 修改collation以匹配fee_prom.shop的utf8mb3
-- **** 修改 WHERE 子句，处理最近30天 (包含今天) 的数据 ****
WHERE lr.付款日期 >= '2025-03-01' -- 修改为指定开始日期
  AND lr.付款日期 <= CURDATE() -- 结束日期为当天
GROUP BY
    lr.付款日期,
    dept.部门名称,
    lr.店铺;