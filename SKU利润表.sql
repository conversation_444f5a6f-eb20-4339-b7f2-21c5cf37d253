-- *********************************************************************************************************************** --
-- ** 注意：此脚本用于计算SKU的毛利和毛利率。                                                                            ** --
-- ** 它会先删除过去30天内已存在的SKU利润数据，然后根据订单明细、退款单、推广费等数据重新计算并插入最新的SKU利润信息。 ** --
-- ** 数据准确性依赖于源表（如订单明细、原始退款单、推广费、平台扣点等）的完整性和准确性。                         ** --
-- ** 请确保在运行此脚本前，相关源数据已经更新至最新状态。                                                              ** --
-- ** 脚本中的日期范围默认为最近30天，可根据需要进行调整。                                                              ** --
-- *********************************************************************************************************************** --

-- 清空过去3天内已存在的数据
DELETE FROM sku利润结果表
WHERE 发货日期 >= DATE_SUB(CURDATE(), INTERVAL 3 DAY) AND 发货日期 <= CURDATE();
-- 将计算后的数据插入到SKU利润结果表
INSERT INTO sku利润结果表 (
    发货日期,
    SKU,
    销售额,
    货品成本,
    售前退款金额,
    退款不退货金额,
    售前退款货品成本,
    退款金额平台扣点费用,
    退货退款金额,
    退货退款货品成本,
    邮费成本,
    售前退款邮资,
    推广费,
    平台扣点费用,
    SKU毛利,
    SKU毛利率
)
WITH 订单商品销售额 AS (
    WITH 填充货品ID AS (
        SELECT 
            订单编号,
            COALESCE(
                平台货品ID,
                FIRST_VALUE(平台货品ID) OVER (
                    PARTITION BY 订单编号 
                    ORDER BY CASE WHEN 平台货品ID != '' AND 平台货品ID IS NOT NULL THEN 0 ELSE 1 END, 平台货品ID
                    ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
                )
            ) as 填充后平台货品ID,
            商家编码, -- SKU维度字段
            订单状态,
            应收金额,
            已付,
            订单明细退款状态,
            COUNT(*) OVER (PARTITION BY 订单编号) as 货品种类数,
            原始子单号
        FROM 订单明细
        WHERE 标记名称 != '贴贴贴贴贴'
          AND 商家编码 IS NOT NULL AND 商家编码 != '' -- 确保SKU字段有效
    )
    SELECT 
        订单编号,
        填充后平台货品ID as 平台货品ID,
        商家编码, -- SKU维度字段
        原始子单号,
        CASE 
            WHEN 订单状态 = '已取消' THEN 应收金额 / 货品种类数
            WHEN 已付 = 0 THEN 应收金额
            ELSE 已付 
        END as 实际金额,
        CASE 
            WHEN 订单状态 = '已取消' THEN 1 / 货品种类数
            ELSE CASE 
                WHEN 已付 = 0 THEN 应收金额
                ELSE 已付 
            END / NULLIF(SUM(CASE 
                WHEN 已付 = 0 THEN 应收金额
                ELSE 已付 
            END) OVER (PARTITION BY 订单编号), 0)
        END as 商品销售额占比
    FROM 填充货品ID
),

-- 原始退款单聚合
原始退款单_聚合 AS (
    SELECT
        原始子单,
        SUM(实际退款金额) as 实际退款金额
    FROM 原始退款单
    WHERE (类型 = '售前退款' OR 类型 = '退款不退货')
    AND 平台状态 = '退款成功'
    GROUP BY 原始子单
),

-- 原始退货单聚合
原始退货单_聚合 AS (
    SELECT
        原始子单,
        SUM(实际退款金额) as 实际退款金额
    FROM 原始退款单
    WHERE 类型 = '退货退款'
    AND 平台状态 = '退款成功'
    GROUP BY 原始子单
),

-- 售前退款子单聚合
售前退款子单_聚合 AS (
    SELECT
        原始子单,
        SUM(实际退款金额) as 实际退款金额
    FROM 原始退款单
    WHERE 类型 = '售前退款'
    AND 平台状态 = '退款成功'
    GROUP BY 原始子单
),

-- 去重订单明细 (按SKU维度，改为发货时间)
DedupOrderDetail AS (
    SELECT DISTINCT
        商家编码, -- 改为SKU维度
        DATE_FORMAT(发货时间, '%Y-%m-%d') as 发货日期,
        原始子单号
    FROM 订单明细
    WHERE 标记名称 != '贴贴贴贴贴'
      AND 商家编码 IS NOT NULL AND 商家编码 != '' -- 改为SKU字段检查
      AND 发货时间 IS NOT NULL AND 发货时间 != '' -- 确保发货时间不为空且不为空字符串
),

-- 售前退款单聚合
售前退款单_聚合 AS (
    SELECT
        原始子单,
        SUM(实际退款金额) as 实际退款金额
    FROM 原始退款单
    WHERE 类型 = '售前退款'
    AND 平台状态 = '退款成功'
    GROUP BY 原始子单
),

-- 退款不退货单聚合
退款不退货单_聚合 AS (
    SELECT
        原始子单,
        SUM(实际退款金额) as 实际退款金额
    FROM 原始退款单
    WHERE 类型 = '退款不退货'
    AND 平台状态 = '退款成功'
    GROUP BY 原始子单
),

-- 新退款金额明细 (按SKU维度，改为发货时间)
新退款金额_明细 AS (
    SELECT
        dod.商家编码, -- 改为SKU维度
        dod.发货日期,
        ROUND(SUM(COALESCE(rtk_sq.实际退款金额, 0)), 4) as 售前退款金额,
        ROUND(SUM(COALESCE(rtk_bth.实际退款金额, 0)), 4) as 退款不退货金额
    FROM DedupOrderDetail dod
    LEFT JOIN 售前退款单_聚合 rtk_sq ON dod.原始子单号 = rtk_sq.原始子单
    LEFT JOIN 退款不退货单_聚合 rtk_bth ON dod.原始子单号 = rtk_bth.原始子单
    GROUP BY
        dod.商家编码, -- 改为SKU维度
        dod.发货日期
),

-- 新退款成本费用明细 (按SKU维度，改为发货时间)
新退款成本费用_明细 AS (
    SELECT
        od.商家编码, -- 改为SKU维度
        DATE_FORMAT(od.发货时间, '%Y-%m-%d') as 发货日期,
        ROUND(SUM(od.货品当前成本), 4) as 售前退款货品成本
    FROM 订单明细 od
    JOIN 售前退款子单_聚合 rtk_sq_cost ON od.原始子单号 = rtk_sq_cost.原始子单
    WHERE od.标记名称 != '贴贴贴贴贴'
      AND od.商家编码 IS NOT NULL AND od.商家编码 != '' -- 改为SKU字段检查
      AND od.发货时间 IS NOT NULL AND od.发货时间 != '' -- 确保发货时间不为空且不为空字符串
    GROUP BY
        od.商家编码, -- 改为SKU维度
        DATE_FORMAT(od.发货时间, '%Y-%m-%d')
),

-- 退货数据 (按SKU维度，改为发货时间)
退货数据 AS (
    WITH DedupOrderDetailForReturn AS (
        SELECT DISTINCT
            商家编码, -- 改为SKU维度
            DATE_FORMAT(发货时间, '%Y-%m-%d') as 发货日期,
            原始子单号
        FROM 订单明细
        WHERE 标记名称 != '贴贴贴贴贴'
          AND 商家编码 IS NOT NULL AND 商家编码 != '' -- 改为SKU字段检查
          AND 发货时间 IS NOT NULL AND 发货时间 != '' -- 确保发货时间不为空且不为空字符串
    )
    SELECT
        dod.商家编码, -- 改为SKU维度
        dod.发货日期,
        ROUND(SUM(COALESCE(rth.实际退款金额, 0)), 4) as 退货退款金额
    FROM DedupOrderDetailForReturn dod
    JOIN 原始退货单_聚合 rth ON dod.原始子单号 = rth.原始子单
    GROUP BY
        dod.商家编码, -- 改为SKU维度
        dod.发货日期
),

-- 邮费数据 (按SKU维度，改为发货时间)
邮费数据 AS (
    SELECT
        t.商家编码, -- 改为SKU维度
        t.发货日期,
        ROUND(SUM(t.分摊邮费), 4) as 邮费成本,
        ROUND(SUM(CASE WHEN rtk_sq_flag.原始子单 IS NOT NULL THEN t.分摊邮费 ELSE 0 END), 4) as 售前退款邮资
    FROM (
        SELECT
            原始子单号,
            商家编码, -- 改为SKU维度
            订单编号, 
            订单状态, 
            发货时间, 
            已付,      
            预估邮资成本, 
            标记名称, 
            DATE_FORMAT(发货时间, '%Y-%m-%d') as 发货日期,
            COALESCE(
                CASE
                    WHEN SUM(CASE WHEN 订单状态 != '已取消' THEN 已付 ELSE 0 END) OVER (PARTITION BY 订单编号) > 0 THEN
                        预估邮资成本 * (已付 / NULLIF(SUM(CASE WHEN 订单状态 != '已取消' THEN 已付 ELSE 0 END) OVER (PARTITION BY 订单编号), 0))
                    WHEN COUNT(CASE WHEN 订单状态 != '已取消' THEN 1 END) OVER (PARTITION BY 订单编号) > 0 THEN
                        预估邮资成本 / NULLIF(COUNT(CASE WHEN 订单状态 != '已取消' THEN 1 END) OVER (PARTITION BY 订单编号), 0)
                    WHEN COUNT(*) OVER (PARTITION BY 订单编号) > 0 THEN
                         预估邮资成本 / COUNT(*) OVER (PARTITION BY 订单编号)
                    ELSE 0
                END,
            0) as 分摊邮费
        FROM 订单明细
        WHERE 标记名称 != '贴贴贴贴贴'
          AND 发货时间 IS NOT NULL AND 发货时间 != '' -- 确保发货时间不为空且不为空字符串
    ) t
    LEFT JOIN 售前退款子单_聚合 rtk_sq_flag ON t.原始子单号 = rtk_sq_flag.原始子单
    GROUP BY t.商家编码, t.发货日期 -- 改为SKU维度分组
),

-- 每日空白ID信息 (改为发货时间)
每日空白ID信息 AS (
    SELECT
        发货日期,
        ROUND(SUM(分摊邮费), 4) as 总空白ID邮费成本,
        COUNT(DISTINCT 店铺名称) as 涉及店铺数
    FROM (
        SELECT
            店铺名称,
            订单编号,
            订单状态,
            已付,
            预估邮资成本,
            DATE_FORMAT(发货时间, '%Y-%m-%d') as 发货日期,
            COALESCE(
                CASE
                    WHEN SUM(CASE WHEN 订单状态 != '已取消' THEN 已付 ELSE 0 END) OVER (PARTITION BY 订单编号) > 0 THEN
                        预估邮资成本 * (已付 / NULLIF(SUM(CASE WHEN 订单状态 != '已取消' THEN 已付 ELSE 0 END) OVER (PARTITION BY 订单编号), 0))
                    WHEN COUNT(CASE WHEN 订单状态 != '已取消' THEN 1 END) OVER (PARTITION BY 订单编号) > 0 THEN
                        预估邮资成本 / NULLIF(COUNT(CASE WHEN 订单状态 != '已取消' THEN 1 END) OVER (PARTITION BY 订单编号), 0)
                    WHEN COUNT(*) OVER (PARTITION BY 订单编号) > 0 THEN
                         预估邮资成本 / COUNT(*) OVER (PARTITION BY 订单编号)
                    ELSE 0
                END,
            0) as 分摊邮费
        FROM 订单明细
        WHERE 标记名称 != '贴贴贴贴贴'
          AND (商家编码 IS NULL OR 商家编码 = '') -- 改为SKU字段检查
          AND 发货时间 IS NOT NULL AND 发货时间 != '' -- 确保发货时间不为空且不为空字符串
    ) t
    GROUP BY 发货日期
),

-- 推广费数据 (按SKU维度分摊)
推广费数据 AS (
    SELECT
        DATE_FORMAT(od.发货时间, '%Y-%m-%d') as 发货日期,
        od.商家编码, -- SKU
        ROUND(SUM(
            COALESCE(p.花费, 0) *
            CASE
                WHEN COALESCE(od_total_paid.总已付_按平台货品ID, 0) = 0 THEN 0 -- Avoid division by zero
                ELSE COALESCE(od.已付, 0) / od_total_paid.总已付_按平台货品ID
            END
        ), 4) as 推广费
    FROM 订单明细 od
    LEFT JOIN 推广费 p ON od.平台货品ID = p.主体ID AND DATE_FORMAT(od.发货时间, '%Y-%m-%d') = DATE_FORMAT(p.日期, '%Y-%m-%d')
    LEFT JOIN (
        -- Calculate total '已付' for each 平台货品ID and 发货日期 to use for apportionment
        SELECT
            平台货品ID,
            DATE_FORMAT(发货时间, '%Y-%m-%d') as 发货日期,
            SUM(COALESCE(已付, 0)) as 总已付_按平台货品ID
        FROM 订单明细
        WHERE 标记名称 != '贴贴贴贴贴'
          AND 平台货品ID IS NOT NULL AND 平台货品ID != ''
          AND 发货时间 IS NOT NULL AND 发货时间 != ''
          AND DATE(发货时间) >= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
          AND DATE(发货时间) <= CURDATE()
        GROUP BY 平台货品ID, DATE_FORMAT(发货时间, '%Y-%m-%d')
    ) od_total_paid ON od.平台货品ID = od_total_paid.平台货品ID AND DATE_FORMAT(od.发货时间, '%Y-%m-%d') = od_total_paid.发货日期
    WHERE od.标记名称 != '贴贴贴贴贴'
      AND od.商家编码 IS NOT NULL AND od.商家编码 != '' -- Ensure SKU is valid
      AND od.发货时间 IS NOT NULL AND od.发货时间 != ''
      AND DATE(od.发货时间) >= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
      AND DATE(od.发货时间) <= CURDATE()
    GROUP BY DATE_FORMAT(od.发货时间, '%Y-%m-%d'), od.商家编码
), -- 成功经验：确保推广费计算也使用相同的3天动态日期范围，并且在商家编码为空时做了处理，避免了因空SKU导致的数据丢失或计算错误。

-- 基础数据 (按SKU维度，改为发货时间)
基础数据 AS (
    SELECT 
        DATE_FORMAT(a.发货时间, '%Y-%m-%d') as 发货日期,
        a.商家编码, -- 改为SKU维度
        ROUND(SUM(CASE 
            WHEN a.标记名称 = '贴贴贴贴贴' THEN 0
            ELSE a.已付
        END), 4) as 销售额,
        ROUND(SUM(a.货品当前成本), 4) as 货品成本,
        ROUND(ANY_VALUE(COALESCE(refund_details.售前退款金额, 0)), 4) as 售前退款金额,
        ROUND(ANY_VALUE(COALESCE(refund_details.退款不退货金额, 0)), 4) as 退款不退货金额,
        ROUND(ANY_VALUE(COALESCE(b2.退货退款金额, 0)), 4) as 退货退款金额,
        ROUND(CASE
            WHEN a.商家编码 IS NOT NULL AND a.商家编码 != '' THEN ANY_VALUE(COALESCE(c.邮费成本, 0)) -- 改为SKU字段检查
            ELSE
                CASE
                    WHEN COALESCE(ANY_VALUE(d_blank.涉及店铺数), 0) > 0 THEN
                        COALESCE(ANY_VALUE(d_blank.总空白ID邮费成本), 0) / ANY_VALUE(d_blank.涉及店铺数)
                    ELSE 0
                END
        END, 4) as 邮费成本,
        ROUND(ANY_VALUE(COALESCE(c.售前退款邮资, 0)), 4) as 售前退款邮资,
        ROUND(ANY_VALUE(COALESCE(g.推广费, 0)), 4) as 推广费,
        ROUND(SUM(CASE WHEN a.标记名称 = '贴贴贴贴贴' THEN 0 ELSE a.已付 END) * ANY_VALUE(COALESCE(h.平台扣点, 0)), 4) as 平台扣点费用,
        ROUND((ANY_VALUE(COALESCE(refund_details.售前退款金额, 0)) + ANY_VALUE(COALESCE(refund_details.退款不退货金额, 0))) * ANY_VALUE(COALESCE(h.平台扣点, 0)), 4) as 退款金额平台扣点费用,
        ROUND(ANY_VALUE(COALESCE(refund_costs_new.售前退款货品成本, 0)), 4) as 售前退款货品成本,
        ROUND(ANY_VALUE(COALESCE(return_costs_new.退货退款货品成本, 0)), 4) as 退货退款货品成本
    FROM 订单明细 a
    LEFT JOIN 新退款金额_明细 refund_details ON a.商家编码 = refund_details.商家编码 AND DATE_FORMAT(a.发货时间, '%Y-%m-%d') = refund_details.发货日期 -- 改为SKU关联
    LEFT JOIN 新退款成本费用_明细 refund_costs_new ON a.商家编码 = refund_costs_new.商家编码 AND DATE_FORMAT(a.发货时间, '%Y-%m-%d') = refund_costs_new.发货日期 -- 改为SKU关联
    LEFT JOIN 退货数据 b2 ON a.商家编码 = b2.商家编码 AND DATE_FORMAT(a.发货时间, '%Y-%m-%d') = b2.发货日期 -- 改为SKU关联
    LEFT JOIN (
        SELECT
            od_sub.商家编码, -- 改为SKU维度
            DATE_FORMAT(od_sub.发货时间, '%Y-%m-%d') as 发货日期,
            ROUND(SUM(od_sub.货品当前成本), 4) as 退货退款货品成本
        FROM 订单明细 od_sub
        JOIN (
            SELECT 原始子单
            FROM 原始退款单
            WHERE 类型 = '退货退款'
            AND 平台状态 = '退款成功'
            GROUP BY 原始子单
        ) rth_cost_sub ON od_sub.原始子单号 = rth_cost_sub.原始子单
        WHERE od_sub.标记名称 != '贴贴贴贴贴'
          AND od_sub.商家编码 IS NOT NULL AND od_sub.商家编码 != '' -- 改为SKU字段检查
          AND od_sub.发货时间 IS NOT NULL AND od_sub.发货时间 != '' -- 确保发货时间不为空且不为空字符串
        GROUP BY
            od_sub.商家编码, -- 改为SKU维度
            DATE_FORMAT(od_sub.发货时间, '%Y-%m-%d')
    ) return_costs_new ON a.商家编码 = return_costs_new.商家编码 AND DATE_FORMAT(a.发货时间, '%Y-%m-%d') = return_costs_new.发货日期 -- 改为SKU关联
    LEFT JOIN 邮费数据 c ON (a.商家编码 = c.商家编码 OR (a.商家编码 IS NULL AND c.商家编码 IS NULL)) AND DATE_FORMAT(a.发货时间, '%Y-%m-%d') = c.发货日期 -- 改为SKU关联
    LEFT JOIN 每日空白ID信息 d_blank ON DATE_FORMAT(a.发货时间, '%Y-%m-%d') = d_blank.发货日期
    -- 推广费按SKU分摊后关联
    LEFT JOIN 推广费数据 g ON a.商家编码 = g.商家编码 AND DATE_FORMAT(a.发货时间, '%Y-%m-%d') = g.发货日期
    LEFT JOIN 平台扣点 h ON a.店铺名称 = h.店铺名称
    WHERE a.标记名称 != '贴贴贴贴贴'
      AND a.商家编码 IS NOT NULL AND a.商家编码 != '' -- 改为SKU字段检查
      AND a.发货时间 IS NOT NULL AND a.发货时间 != '' -- 确保发货时间不为空且不为空字符串
      -- 成功经验：之前修复了空字符串''被视为日期导致的1292错误，通过添加 发货时间 != '' 条件。
      -- 同时，日期范围从固定日期改为动态获取最近3天的数据。
      AND DATE(a.发货时间) >= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
      AND DATE(a.发货时间) <= CURDATE()
    GROUP BY 
        DATE_FORMAT(a.发货时间, '%Y-%m-%d'),
        a.商家编码 -- 改为SKU维度
)

-- 主查询：SKU利润统计
SELECT 
    发货日期,
    商家编码 as SKU, -- SKU维度
    销售额,
    货品成本,
    售前退款金额,
    退款不退货金额,
    售前退款货品成本,
    退款金额平台扣点费用,
    退货退款金额,
    退货退款货品成本,
    邮费成本,
    售前退款邮资,
    推广费,
    平台扣点费用,
    -- SKU毛利计算
    ROUND(
        (COALESCE(销售额, 0) - COALESCE(售前退款金额, 0) - COALESCE(退货退款金额, 0) - COALESCE(退款不退货金额, 0))
        - (COALESCE(货品成本, 0) - COALESCE(售前退款货品成本, 0) + COALESCE(退货退款货品成本, 0))
        - (COALESCE(邮费成本, 0) - COALESCE(售前退款邮资, 0))
        - (COALESCE(平台扣点费用, 0) - COALESCE(退款金额平台扣点费用, 0))
        - COALESCE(推广费, 0),
        4
    ) as SKU毛利,
    -- SKU毛利率计算
    CONCAT(ROUND(
        LEAST(9999.99, GREATEST(-9999.99, 
            (
                销售额 - 
                货品成本 - 
                (COALESCE(售前退款金额, 0) + COALESCE(退款不退货金额, 0)) -
                COALESCE(退货退款金额, 0) - 
                COALESCE(邮费成本, 0) -
                COALESCE(推广费, 0) -
                平台扣点费用 +
                COALESCE(售前退款货品成本, 0) +
                COALESCE(退款金额平台扣点费用, 0) +
                COALESCE(退货退款货品成本, 0)
            ) * 100 / 
            NULLIF(销售额 - (COALESCE(售前退款金额, 0) + COALESCE(退款不退货金额, 0)) - COALESCE(退货退款金额, 0), 0) 
        ))
    , 2), '%') as SKU毛利率
FROM 基础数据
ORDER BY 发货日期 DESC, SKU;