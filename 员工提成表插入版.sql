-- 先清空员工提成结果表
TRUNCATE TABLE 员工提成结果表;

-- 将结果插入到员工提成结果表 (调整列顺序)
INSERT INTO 员工提成结果表 (
    付款月份,
    员工,
    店铺,
    平台货品ID,
    发布日期,
    品类,
    任务类型列表,
    任务权重,
    美工成本,
    拍摄成本,
    链接固定成本,
    链接毛利,
    店铺权重,
    品类提点,
    上架时间权重,
    链接总提成,
    员工提成计算过程,
    员工提成
)

-- 定义任务权重映射
WITH 任务权重映射 AS (
    -- 标品早期的任务权重
    SELECT '标品早期' as 品类, '发现优质市场' as 任务类型, 0.1 as 权重
    UNION ALL SELECT '标品早期', '确定赛道可做', 0.15
    UNION ALL SELECT '标品早期', '与采购搞定货品', 0.25
    UNION ALL SELECT '标品早期', '同行利润率分析', 0.045
    UNION ALL SELECT '标品早期', '人群分析', 0.045
    UNION ALL SELECT '标品早期', '竞争对手跟踪', 0.0675
    UNION ALL SELECT '标品早期', '产品定价和营销', 0.1125
    UNION ALL SELECT '标品早期', '视觉规划', 0.09
    UNION ALL SELECT '标品早期', '付费推广', 0.045
    UNION ALL SELECT '标品早期', '评价维护', 0.045
    UNION ALL SELECT '标品早期', '评价布局+S单', 0.05
    
    -- 标品成熟期的任务权重
    UNION ALL SELECT '标品成熟期', '发现优质市场', 0.04
    UNION ALL SELECT '标品成熟期', '确定赛道可做', 0.06
    UNION ALL SELECT '标品成熟期', '与采购搞定货品', 0.1
    UNION ALL SELECT '标品成熟期', '同行利润率分析', 0.075
    UNION ALL SELECT '标品成熟期', '人群分析', 0.0375
    UNION ALL SELECT '标品成熟期', '竞争对手跟踪', 0.1125
    UNION ALL SELECT '标品成熟期', '产品定价和营销', 0.225
    UNION ALL SELECT '标品成熟期', '视觉规划', 0.1125
    UNION ALL SELECT '标品成熟期', '付费推广', 0.1125
    UNION ALL SELECT '标品成熟期', '评价维护', 0.075
    UNION ALL SELECT '标品成熟期', '评价布局+S单', 0.05
    
    -- 半标品成熟期的任务权重
    UNION ALL SELECT '半标品成熟期', '发现优质市场', 0.06
    UNION ALL SELECT '半标品成熟期', '确定赛道可做', 0.09
    UNION ALL SELECT '半标品成熟期', '与采购搞定货品', 0.15
    UNION ALL SELECT '半标品成熟期', '同行利润率分析', 0.06
    UNION ALL SELECT '半标品成熟期', '人群分析', 0.03
    UNION ALL SELECT '半标品成熟期', '竞争对手跟踪', 0.09
    UNION ALL SELECT '半标品成熟期', '产品定价和营销', 0.12
    UNION ALL SELECT '半标品成熟期', '视觉规划', 0.15
    UNION ALL SELECT '半标品成熟期', '付费推广', 0.06
    UNION ALL SELECT '半标品成熟期', '评价维护', 0.09
    UNION ALL SELECT '半标品成熟期', '评价布局+S单', 0.1
),

-- 获取任务分工信息（修改后的版本）
任务分工 AS (
    SELECT DISTINCT  
        t.链接ID, 
        t.任务类型 as 任务名称,
        t.负责人,
        CASE 
            WHEN m.权重 IS NULL THEN '未设置该品类任务权重'
            ELSE CAST(m.权重 AS CHAR)
        END as 任务权重,
        b.品类,
        m.权重 as 原始权重
    FROM (
        SELECT DISTINCT 链接ID, '发现优质市场' as 任务类型, 发现优质市场 as 负责人 FROM 链接分工 WHERE 发现优质市场 IS NOT NULL
        UNION ALL
        SELECT DISTINCT 链接ID, '确定赛道可做', 确定赛道可做 FROM 链接分工 WHERE 确定赛道可做 IS NOT NULL
        UNION ALL
        SELECT DISTINCT 链接ID, '与采购搞定货品', 与采购搞定货品 FROM 链接分工 WHERE 与采购搞定货品 IS NOT NULL
        UNION ALL
        SELECT DISTINCT 链接ID, '同行利润率分析', 同行利润率分析 FROM 链接分工 WHERE 同行利润率分析 IS NOT NULL
        UNION ALL
        SELECT DISTINCT 链接ID, '人群分析', 人群分析 FROM 链接分工 WHERE 人群分析 IS NOT NULL
        UNION ALL
        SELECT DISTINCT 链接ID, '竞争对手跟踪', 竞争对手跟踪 FROM 链接分工 WHERE 竞争对手跟踪 IS NOT NULL
        UNION ALL
        SELECT DISTINCT 链接ID, '产品定价和营销', 产品定价和营销 FROM 链接分工 WHERE 产品定价和营销 IS NOT NULL
        UNION ALL
        SELECT DISTINCT 链接ID, '视觉规划', 视觉规划 FROM 链接分工 WHERE 视觉规划 IS NOT NULL
        UNION ALL
        SELECT DISTINCT 链接ID, '付费推广', 付费推广 FROM 链接分工 WHERE 付费推广 IS NOT NULL
        UNION ALL
        SELECT DISTINCT 链接ID, '评价维护', 评价维护 FROM 链接分工 WHERE 评价维护 IS NOT NULL
        UNION ALL
        SELECT DISTINCT 链接ID, '评价布局+S单', `评价布局+S单` FROM 链接分工 WHERE `评价布局+S单` IS NOT NULL
    ) t
    LEFT JOIN 链接权重 b ON t.链接ID = b.ID COLLATE utf8mb4_general_ci
    LEFT JOIN 任务权重映射 m ON b.品类 = m.品类 COLLATE utf8mb4_general_ci AND t.任务类型 = m.任务类型 COLLATE utf8mb4_general_ci
),

-- 计算每个员工的任务权重（修改后的版本）
员工任务权重 AS (
    SELECT 
        链接ID,
        负责人,
        品类,
        GROUP_CONCAT(
            DISTINCT CASE 
                WHEN 任务权重 = '未设置该品类任务权重' THEN 任务名称
                ELSE CONCAT(任务名称, '*', 任务权重)
            END
            ORDER BY 任务名称 
            SEPARATOR ', '
        ) as 任务类型列表,
        CASE 
            WHEN COUNT(CASE WHEN 任务权重 = '未设置该品类任务权重' THEN 1 END) > 0 THEN '未设置该品类任务权重'
            ELSE CAST(SUM(原始权重) AS CHAR)
        END as 任务权重
    FROM 任务分工
    GROUP BY 链接ID, 负责人, 品类
)

-- 主查询 (调整列顺序)
SELECT 
    COALESCE(DATE_FORMAT(p.付款日期, '%Y-%m'), '暂无销售数据') as 付款月份,
    w.负责人 as 员工,
    p.店铺 as 店铺,
    w.链接ID as 平台货品ID,
    d.发布日期,
    d.品类 as 品类,
    w.任务类型列表,
    w.任务权重,
    ROUND(COALESCE(d.美工成本, 0), 4) as 美工成本,
    ROUND(COALESCE(d.拍照成本, 0), 4) as 拍摄成本,
    CASE 
        WHEN d.商品级别 IN ('S级', 'A级', 'B级') THEN 100
        WHEN d.商品级别 = 'C级' THEN 50
        ELSE 0
    END as 链接固定成本,
    -- 链接毛利 (调整后)
    ROUND(
        SUM(COALESCE(p.链接毛利, 0))
        - COALESCE(d.美工成本, 0)
        - COALESCE(d.拍照成本, 0)
        - CASE WHEN d.商品级别 IN ('S级', 'A级', 'B级') THEN 100 WHEN d.商品级别 = 'C级' THEN 50 ELSE 0 END,
        4
    ) as 链接毛利,
    ROUND(COALESCE(p.店铺权重, 0), 4) as 店铺权重,
    p.品类提点 as 品类提点, -- 原始百分比字符串
    ROUND(COALESCE(p.上架时间权重, 0), 4) as 上架时间权重,
    -- 链接总提成
    ROUND(
        (
            SUM(COALESCE(p.链接毛利, 0))
            - COALESCE(d.美工成本, 0)
            - COALESCE(d.拍照成本, 0)
            - CASE WHEN d.商品级别 IN ('S级', 'A级', 'B级') THEN 100 WHEN d.商品级别 = 'C级' THEN 50 ELSE 0 END
        ) *
        COALESCE(p.店铺权重, 0) *
        (CAST(REPLACE(COALESCE(NULLIF(p.品类提点, ''), '0%'), '%', '') AS DECIMAL(10,4)) / 100.0) *
        COALESCE(p.上架时间权重, 0),
        4
    ) as 链接总提成,
    -- 员工提成计算过程
    CONCAT(
        '链接总提成[',
         ROUND(
            (
                SUM(COALESCE(p.链接毛利, 0))
                - COALESCE(d.美工成本, 0)
                - COALESCE(d.拍照成本, 0)
                - CASE WHEN d.商品级别 IN ('S级', 'A级', 'B级') THEN 100 WHEN d.商品级别 = 'C级' THEN 50 ELSE 0 END
            ) *
            COALESCE(p.店铺权重, 0) *
            (CAST(REPLACE(COALESCE(NULLIF(p.品类提点, ''), '0%'), '%', '') AS DECIMAL(10,4)) / 100.0) *
            COALESCE(p.上架时间权重, 0),
            4
        ),
        '] * 任务权重[',
        CASE
            WHEN w.任务权重 = '未设置该品类任务权重' THEN '0'
            ELSE w.任务权重
        END,
        ']'
    ) as 员工提成计算过程,
    -- 员工提成
    ROUND(
        (
                 ROUND(
                    (
                        SUM(COALESCE(p.链接毛利, 0))
                        - COALESCE(d.美工成本, 0)
                        - COALESCE(d.拍照成本, 0)
                        - CASE WHEN d.商品级别 IN ('S级', 'A级', 'B级') THEN 100 WHEN d.商品级别 = 'C级' THEN 50 ELSE 0 END
                    ) *
                    COALESCE(p.店铺权重, 0) *
                    (CAST(REPLACE(COALESCE(NULLIF(p.品类提点, ''), '0%'), '%', '') AS DECIMAL(10,4)) / 100.0) *
                    COALESCE(p.上架时间权重, 0),
                    4
                )
            ) *
            CASE
                WHEN w.任务权重 = '未设置该品类任务权重' THEN 0
                ELSE CAST(w.任务权重 AS DECIMAL(10,4))
            END,
        4
    ) as 员工提成
FROM 员工任务权重 w
LEFT JOIN 链接利润结果表 p ON w.链接ID = p.平台货品ID COLLATE utf8mb4_general_ci
LEFT JOIN 链接权重 d ON w.链接ID = d.ID COLLATE utf8mb4_general_ci
WHERE w.负责人 IS NOT NULL 
  AND w.负责人 != ''
GROUP BY 
    COALESCE(DATE_FORMAT(p.付款日期, '%Y-%m'), '暂无销售数据'),
    w.负责人,
    p.店铺,
    w.链接ID,
    d.发布日期,
    d.品类,
    w.任务类型列表,
    w.任务权重,
    d.美工成本,
    d.拍照成本,
    d.商品级别,
    p.店铺权重,
    p.品类提点,
    p.上架时间权重
ORDER BY 付款月份 DESC, w.链接ID, w.负责人;