-- ========================================================================
-- 查询指定日期和店铺的退款不退货金额对应的订单明细
-- 日期：2025-06-10
-- 店铺：海宝活欣工艺品有限公司
-- ========================================================================

-- 设置查询参数
SET @target_date = '2025-06-10';
SET @target_store = '海宝活欣工艺品有限公司';

-- 主查询：显示退款不退货的详细信息
WITH 
-- 1. 筛选退款不退货类型的退款单
退款不退货单_明细 AS (
    SELECT
        原始子单,
        实际退款金额,
        退款原因,
        退款时间,
        退款成功时间,
        店铺,
        货品,
        网名
    FROM 原始退款单
    WHERE 类型 = '退款不退货' COLLATE utf8mb4_general_ci
    AND 平台状态 = '退款成功' COLLATE utf8mb4_general_ci
),

-- 2. 获取去重的订单明细
订单明细_去重 AS (
    SELECT DISTINCT
        平台货品ID,
        DATE_FORMAT(STR_TO_DATE(付款时间, '%Y-%m-%d %H:%i:%s'), '%Y-%m-%d') as 付款日期,
        原始子单号,
        店铺名称,
        订单编号,
        货品名称,
        平台货品名称,
        已付,
        应收金额,
        货品当前成本,
        订单状态,
        客户网名,
        收件人,
        省市县
    FROM 订单明细
    WHERE 标记名称 != '贴贴贴贴贴' COLLATE utf8mb4_general_ci
      AND DATE_FORMAT(STR_TO_DATE(付款时间, '%Y-%m-%d %H:%i:%s'), '%Y-%m-%d') = @target_date
      AND 店铺名称 = @target_store COLLATE utf8mb4_general_ci
      AND 平台货品ID IS NOT NULL
      AND 平台货品ID != '' COLLATE utf8mb4_general_ci
),

-- 3. 关联退款不退货数据
关联退款明细 AS (
    SELECT
        od.平台货品ID,
        od.付款日期,
        od.店铺名称,
        od.订单编号,
        od.原始子单号,
        od.货品名称,
        od.平台货品名称,
        od.已付,
        od.应收金额,
        od.货品当前成本,
        od.订单状态,
        od.客户网名,
        od.收件人,
        od.省市县,
        rtk.实际退款金额 as 退款不退货金额,
        rtk.退款原因,
        rtk.退款时间,
        rtk.退款成功时间,
        rtk.网名 as 退款人网名
    FROM 订单明细_去重 od
    INNER JOIN 退款不退货单_明细 rtk ON od.原始子单号 = rtk.原始子单 COLLATE utf8mb4_general_ci
)

-- 主查询结果：订单明细信息
SELECT 
    '订单明细信息' as 数据类型,
    平台货品ID,
    付款日期,
    店铺名称,
    订单编号,
    原始子单号,
    货品名称,
    平台货品名称,
    已付,
    应收金额,
    货品当前成本,
    订单状态,
    客户网名,
    收件人,
    省市县,
    退款不退货金额,
    退款原因,
    退款时间,
    退款成功时间,
    退款人网名
FROM 关联退款明细
ORDER BY 平台货品ID, 原始子单号;

-- ========================================================================
-- 汇总统计信息
-- ========================================================================
WITH 关联退款明细_汇总 AS (
    SELECT
        od.平台货品ID,
        od.付款日期,
        od.店铺名称,
        od.订单编号,
        od.原始子单号,
        od.货品名称,
        od.平台货品名称,
        od.已付,
        od.应收金额,
        od.货品当前成本,
        od.订单状态,
        rtk.实际退款金额 as 退款不退货金额
    FROM (
        SELECT DISTINCT
            平台货品ID,
            DATE_FORMAT(STR_TO_DATE(付款时间, '%Y-%m-%d %H:%i:%s'), '%Y-%m-%d') as 付款日期,
            原始子单号,
            店铺名称,
            订单编号,
            货品名称,
            平台货品名称,
            已付,
            应收金额,
            货品当前成本,
            订单状态
        FROM 订单明细
        WHERE 标记名称 != '贴贴贴贴贴' COLLATE utf8mb4_general_ci
          AND DATE_FORMAT(STR_TO_DATE(付款时间, '%Y-%m-%d %H:%i:%s'), '%Y-%m-%d') = @target_date
          AND 店铺名称 = @target_store COLLATE utf8mb4_general_ci
          AND 平台货品ID IS NOT NULL
          AND 平台货品ID != '' COLLATE utf8mb4_general_ci
    ) od
    INNER JOIN (
        SELECT
            原始子单,
            实际退款金额
        FROM 原始退款单
        WHERE 类型 = '退款不退货' COLLATE utf8mb4_general_ci
        AND 平台状态 = '退款成功' COLLATE utf8mb4_general_ci
    ) rtk ON od.原始子单号 = rtk.原始子单 COLLATE utf8mb4_general_ci
)
SELECT 
    '汇总统计' as 数据类型,
    NULL as 平台货品ID,
    @target_date as 付款日期,
    @target_store as 店铺名称,
    CONCAT('共', COUNT(DISTINCT 订单编号), '个订单') as 订单编号,
    CONCAT('共', COUNT(DISTINCT 原始子单号), '个子单') as 原始子单号,
    CONCAT('共', COUNT(DISTINCT 平台货品ID), '个平台货品') as 货品名称,
    NULL as 平台货品名称,
    ROUND(SUM(已付), 2) as 已付,
    ROUND(SUM(应收金额), 2) as 应收金额,
    ROUND(SUM(货品当前成本), 2) as 货品当前成本,
    NULL as 订单状态,
    ROUND(SUM(退款不退货金额), 2) as 退款不退货金额,
    NULL as 客户网名,
    NULL as 收件人,
    NULL as 省市县,
    NULL as 退款原因,
    NULL as 退款时间,
    NULL as 退款成功时间,
    NULL as 退款人网名
FROM 关联退款明细_汇总;

-- ========================================================================
-- 验证查询：对比链接利润表中的退款不退货金额汇总
-- ========================================================================
SELECT 
    '链接利润表对比' as 验证类型,
    平台货品ID,
    店铺,
    退款不退货金额
FROM 链接利润结果表
WHERE 付款日期 = @target_date
  AND 店铺 = @target_store COLLATE utf8mb4_general_ci
  AND 退款不退货金额 > 0
ORDER BY 退款不退货金额 DESC;

-- ========================================================================
-- 补充查询：显示指定日期店铺的所有退款不退货记录（不限制平台货品ID）
-- ========================================================================
SELECT 
    '全部退款不退货记录' as 数据类型,
    rtk.店铺,
    rtk.原始子单,
    rtk.货品,
    rtk.网名,
    rtk.实际退款金额,
    rtk.退款原因,
    rtk.退款时间,
    rtk.退款成功时间
FROM 原始退款单 rtk
WHERE rtk.类型 = '退款不退货' COLLATE utf8mb4_general_ci
  AND rtk.平台状态 = '退款成功' COLLATE utf8mb4_general_ci
  AND rtk.店铺 = @target_store COLLATE utf8mb4_general_ci
  AND (
      rtk.退款时间 LIKE CONCAT(@target_date, '%') OR
      rtk.退款成功时间 LIKE CONCAT(@target_date, '%')
  )
ORDER BY rtk.退款成功时间, rtk.原始子单; 